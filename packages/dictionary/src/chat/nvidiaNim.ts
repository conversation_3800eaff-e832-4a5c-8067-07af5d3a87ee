import OpenAI from 'openai';
import { ChatMessage, ChatResponse, ChatSource } from './types';
import { ChatCompletionMessageParam } from 'openai/resources';
import { ChatCompletion } from 'openai/resources';
import { removeThinkBlocks } from '../sources/utils';

export class NvidiaNimChat implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName: string;
  isThinkingModel = false;
  maxTokens = 8192;
  openAI: OpenAI;
  messages: ChatCompletionMessageParam[] = [];

  constructor(config: { 
    modelName: string, 
    modelId: number, 
    maxToken?: number, 
    apiKey: string,
    isThinkingModel?: boolean 
  }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config?.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.isThinkingModel = config.isThinkingModel ?? false;
    this.openAI = new OpenAI({
      apiKey: this.apiKey,
      baseURL: 'https://integrate.api.nvidia.com/v1',
      dangerouslyAllowBrowser: true
    });

    if (this.shouldAddNothinkPrefix()) {
      this.messages.push({
        role: 'system',
        content: '/nothink'
      });
    }
  }

  private shouldAddNothinkPrefix(): boolean {
    return !this.isThinkingModel && this.modelName.toLowerCase().includes('qwen-3');
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages;
  }

  toChatResponse (result: ChatCompletion): ChatResponse {
    const message = removeThinkBlocks(result.choices[0].message.content as string);
    return {
      message,
      usage: {
        promptTokens: result.usage?.prompt_tokens,
        completionTokens: result.usage?.completion_tokens,
        totalTokens: result.usage?.total_tokens,
      },
      modelId: this.modelId
    };
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    this.messages.push({ role: 'user', content: message });

    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: this.messages,
      max_tokens: this.maxTokens,
      temperature: 0.5,
      top_p: 1.0,
      frequency_penalty: 0.0,
      presence_penalty: 0.0,
    });
    
    const chatResponse = this.toChatResponse(completion);
    this.messages.push({
      role: 'assistant',
      content: chatResponse.message
    });
    
    return chatResponse;
  }

  async *sendMessageStream(message: string): AsyncGenerator<{ chunk: string; isComplete: boolean; result?: ChatResponse }> {
    this.messages.push({ role: 'user', content: message });

    const stream = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: this.messages,
      max_tokens: this.maxTokens,
      temperature: 0.5,
      top_p: 1.0,
      frequency_penalty: 0.0,
      presence_penalty: 0.0,
      stream: true,
    });

    let fullText = '';
    let usage: { prompt_tokens?: number; completion_tokens?: number; total_tokens?: number } | undefined;
    
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        fullText += content;
        
        yield {
          chunk: content,
          isComplete: false
        };
      }
      
      // Capture usage info if available
      if (chunk.usage) {
        usage = chunk.usage;
      }
    }
    
    console.log(`${this.modelName}-sendMessageStream complete:`, fullText);
    
    // Remove think blocks from the full text
    const cleanedMessage = removeThinkBlocks(fullText);
    
    // Add response to history
    this.messages.push({
      role: 'assistant',
      content: cleanedMessage
    });
    
    // Yield final result
    yield {
      chunk: '',
      isComplete: true,
      result: {
        message: cleanedMessage,
        usage: usage ? {
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          totalTokens: usage.total_tokens,
        } : undefined,
        modelId: this.modelId
      }
    };
  }
}