import { marked } from 'marked';
import { PartOfSpeech } from '../types';

// Define the interface for MarkedToken (consistent with existing codebase)
interface MarkedToken {
  type: string;
  raw?: string;
  text?: string;
  tokens?: MarkedToken[];
  items?: MarkedToken[];
  depth?: number;
  [key: string]: any;
}

export interface WordInContextDefinition {
  definition: string;
  partOfSpeech: PartOfSpeech;
  explanation: string;
  examples: {
    example: string;
    explanation: string;
  }[];
  synonyms: {
    synonym: string;
    example: string;
    explanation: string;
  }[];
}

/**
 * Helper function to decode HTML entities (both named and numeric)
 */
function decodeHtmlEntities(text: string): string {
  return text
    .replace(/&#(\d+);/g, (_, num) => String.fromCharCode(parseInt(num, 10)))
    .replace(/&#x([0-9a-fA-F]+);/g, (_, hex) => String.fromCharCode(parseInt(hex, 16)))
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'")
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&');
}

/**
 * Extract text from marked tokens
 */
function getTextFromMarkedTokens(tokens: MarkedToken[]): string {
  return tokens.map(token => getTextFromMarkedToken(token)).join('');
}

/**
 * Extract text from a single marked token
 */
function getTextFromMarkedToken(token: MarkedToken): string {
  switch (token.type) {
    case 'text':
      return decodeHtmlEntities(token.text || '');
    case 'strong':
      const strongText = token.tokens ? getTextFromMarkedTokens(token.tokens) : decodeHtmlEntities(token.text || '');
      return strongText; // Return without bold formatting for easier parsing
    case 'em':
      const emText = token.tokens ? getTextFromMarkedTokens(token.tokens) : decodeHtmlEntities(token.text || '');
      return emText; // Return without italic formatting for easier parsing
    case 'link':
      return token.tokens ? getTextFromMarkedTokens(token.tokens) : decodeHtmlEntities(token.text || '');
    case 'code':
      return decodeHtmlEntities(token.text || '');
    case 'br':
      return '\n';
    case 'space':
      return ' ';
    default:
      if ('tokens' in token && token.tokens) {
        return getTextFromMarkedTokens(token.tokens);
      }
      if ('text' in token && typeof token.text === 'string') {
        return decodeHtmlEntities(token.text);
      }
      return '';
  }
}

/**
 * Parse markdown response from LLM into structured WordInContextDefinition object
 * Error-tolerant parser following the same patterns as other parsers in the codebase
 */
export function parseMarkdownMeaningInContext(markdown: string): WordInContextDefinition {
  const tokens = marked.lexer(markdown);
  
  // Initialize with defaults
  const definition: WordInContextDefinition = {
    definition: '',
    partOfSpeech: 'noun',
    explanation: '',
    examples: [],
    synonyms: []
  };
  
  let currentSection: string | null = null;
  let currentListContext: 'examples' | 'synonyms' | null = null;
  
  const traverse = (token: MarkedToken) => {
    // Handle heading tokens
    if (token.type === 'heading') {
      const headingText = token.tokens 
        ? getTextFromMarkedTokens(token.tokens) 
        : decodeHtmlEntities(token.text || '').trim();
      
      const normalizedHeading = headingText.toLowerCase().trim();
      
      // Detect sections based on heading content
      if (normalizedHeading.includes('definition')) {
        currentSection = 'definition';
        currentListContext = null;
      } else if (normalizedHeading.includes('part of speech')) {
        currentSection = 'partOfSpeech';
        currentListContext = null;
      } else if (normalizedHeading.includes('explanation')) {
        currentSection = 'explanation';
        currentListContext = null;
      } else if (normalizedHeading.includes('examples') || normalizedHeading.includes('example')) {
        currentSection = 'examples';
        currentListContext = 'examples';
      } else if (normalizedHeading.includes('synonyms') || normalizedHeading.includes('synonym')) {
        currentSection = 'synonyms';
        currentListContext = 'synonyms';
      }
    }
    
    // Handle paragraph content
    else if (token.type === 'paragraph' && currentSection) {
      const paragraphText = token.tokens 
        ? getTextFromMarkedTokens(token.tokens) 
        : decodeHtmlEntities(token.text || '').trim();
      
      if (currentSection === 'definition') {
        definition.definition = paragraphText;
      } else if (currentSection === 'partOfSpeech') {
        // Extract part of speech, handling common formats
        const pos = paragraphText.toLowerCase().trim();
        definition.partOfSpeech = pos as PartOfSpeech;
      } else if (currentSection === 'explanation') {
        if (definition.explanation) {
          definition.explanation += ' ' + paragraphText;
        } else {
          definition.explanation = paragraphText;
        }
      }
    }
    
    // Handle list tokens
    else if (token.type === 'list' && currentListContext) {
      if (token.items && Array.isArray(token.items)) {
        token.items.forEach((item: MarkedToken) => {
          parseListItem(item, definition, currentListContext!);
        });
      }
    }
    
    // Recursive traversal for nested tokens (skip list tokens if already handled above)
    if (token.tokens && Array.isArray(token.tokens)) {
      token.tokens.forEach(traverse);
    }
    
    // Handle nested lists (only if not already processed in list context)
    if (token.type === 'list' && token.items && Array.isArray(token.items) && !currentListContext) {
      token.items.forEach(traverse);
    }
  };
  
  // Start traversal
  tokens.forEach(traverse);
  
  // Set defaults if still empty
  if (!definition.definition) {
    definition.definition = 'Definition not found';
  }
  if (!definition.explanation) {
    definition.explanation = 'Explanation not found';
  }
  
  return definition;
}

/**
 * Parse individual list items for examples and synonyms
 */
function parseListItem(item: MarkedToken, definition: WordInContextDefinition, context: 'examples' | 'synonyms') {
  const itemText = item.tokens 
    ? getTextFromMarkedTokens(item.tokens) 
    : decodeHtmlEntities(item.text || '').trim();
  
  if (context === 'examples') {
    // Check for new format: "Example: [text]" or "Explanation: [text]"
    if (itemText.startsWith('Example:')) {
      const exampleText = itemText.replace(/^Example:\s*/, '').trim();
      // Store incomplete example, will be completed when explanation is found
      definition.examples.push({
        example: exampleText,
        explanation: '' // Empty until explanation is found
      });
    } else if (itemText.startsWith('Explanation:') && definition.examples.length > 0) {
      // Update the last example's explanation
      const explanationText = itemText.replace(/^Explanation:\s*/, '').trim();
      definition.examples[definition.examples.length - 1].explanation = explanationText;
    } else {
      // Fallback to original nested format
      const exampleText = extractMainListText(item);
      const explanation = extractSubListText(item);
      
      if (exampleText) {
        definition.examples.push({
          example: exampleText,
          explanation: explanation || ''
        });
      }
    }
  } else if (context === 'synonyms') {
    // Check for new format: "Synonym: [text]", "Example: [text]", "Explanation: [text]"
    if (itemText.startsWith('Synonym:')) {
      const synonymText = itemText.replace(/^Synonym:\s*/, '').trim();
      definition.synonyms.push({
        synonym: synonymText.toLowerCase(),
        example: '',
        explanation: ''
      });
    } else if (itemText.startsWith('Example:') && definition.synonyms.length > 0) {
      const exampleText = itemText.replace(/^Example:\s*/, '').trim();
      definition.synonyms[definition.synonyms.length - 1].example = exampleText;
    } else if (itemText.startsWith('Explanation:') && definition.synonyms.length > 0) {
      const explanationText = itemText.replace(/^Explanation:\s*/, '').trim();
      definition.synonyms[definition.synonyms.length - 1].explanation = explanationText;
    } else {
      // Fallback to original format
      if (item.tokens && item.tokens.some(t => t.type === 'list')) {
        // This item has nested lists
        const mainText = extractMainListText(item);
        const explanation = extractSubListText(item);
        
        if (mainText) {
          const synonymMatch = mainText.match(/^\*\*([^*]+?)(?::)?\*\*:?\s*(.*)$/) || mainText.match(/^([^:]+?):\s*(.*)$/);
          if (synonymMatch) {
            definition.synonyms.push({
              synonym: synonymMatch[1].trim().toLowerCase(),
              example: synonymMatch[2].trim(),
              explanation: explanation || ''
            });
          } else {
            // Fallback: treat whole text as synonym
            definition.synonyms.push({
              synonym: mainText.replace(/^\*\*(.*?)\*\*$/, '$1').trim().toLowerCase(),
              example: '',
              explanation: explanation || ''
            });
          }
        }
      } else {
        // Simple list item - parse synonym:example format
        const synonymMatch = itemText.match(/^\*\*([^*]+?)(?::)?\*\*:?\s*(.*)$/) || itemText.match(/^([^:]+?):\s*(.*)$/);
        if (synonymMatch) {
          definition.synonyms.push({
            synonym: synonymMatch[1].trim().toLowerCase(),
            example: synonymMatch[2] ? synonymMatch[2].trim() : '',
            explanation: ''
          });
        }
      }
    }
  }
}

/**
 * Extract main text from a list item (excluding nested lists)
 */
function extractMainListText(item: MarkedToken): string {
  if (!item.tokens) {
    // Fallback to item text if no tokens
    return item.text ? decodeHtmlEntities(item.text).trim() : '';
  }
  
  let mainText = '';
  for (const token of item.tokens) {
    if (token.type !== 'list') {
      mainText += getTextFromMarkedToken(token);
    }
  }
  return mainText.trim();
}

/**
 * Extract text from nested lists (sub-explanations)
 */
function extractSubListText(item: MarkedToken): string {
  if (!item.tokens) return '';
  
  let subText = '';
  for (const token of item.tokens) {
    if (token.type === 'list' && token.items) {
      for (const subItem of token.items) {
        const subItemText = subItem.tokens 
          ? getTextFromMarkedTokens(subItem.tokens) 
          : decodeHtmlEntities(subItem.text || '').trim();
        if (subText) {
          subText += ' ' + subItemText;
        } else {
          subText = subItemText;
        }
      }
    }
  }
  return subText.trim();
}

/**
 * Streaming-compatible parser that handles partial markdown content
 * Returns Partial<WordInContextDefinition> without any eager initialization
 * Reuses the existing parseMarkdownMeaningInContext logic completely
 */
export function parsePartialMarkdownMeaningInContext(markdown: string): Partial<WordInContextDefinition> {
  try {
    // Use the existing full parser
    const fullDefinition = parseMarkdownMeaningInContext(markdown);
    
    // Convert to partial by only including non-empty/meaningful fields
    const partialDefinition: Partial<WordInContextDefinition> = {};
    
    if (fullDefinition.definition && fullDefinition.definition !== 'Definition not found') {
      partialDefinition.definition = fullDefinition.definition;
    }
    
    if (fullDefinition.partOfSpeech && fullDefinition.partOfSpeech.trim() !== '') {
      partialDefinition.partOfSpeech = fullDefinition.partOfSpeech;
    }
    
    if (fullDefinition.explanation && fullDefinition.explanation !== 'Explanation not found') {
      partialDefinition.explanation = fullDefinition.explanation;
    }
    
    if (fullDefinition.examples && fullDefinition.examples.length > 0) {
      // Include examples as long as they have example text (explanation can be empty during streaming)
      const filteredExamples = fullDefinition.examples.filter(example => 
        example.example && example.example.trim() !== ''
      );
      if (filteredExamples.length > 0) {
        partialDefinition.examples = filteredExamples;
      }
    }
    
    if (fullDefinition.synonyms && fullDefinition.synonyms.length > 0) {
      // Include synonyms as long as they have synonym text (explanation can be empty during streaming)
      const filteredSynonyms = fullDefinition.synonyms.filter(synonym => 
        synonym.synonym && synonym.synonym.trim() !== ''
      );
      if (filteredSynonyms.length > 0) {
        partialDefinition.synonyms = filteredSynonyms;
      }
    }
    
    return partialDefinition;
    
  } catch (error) {
    // Return empty object if parsing fails completely
    return {};
  }
}