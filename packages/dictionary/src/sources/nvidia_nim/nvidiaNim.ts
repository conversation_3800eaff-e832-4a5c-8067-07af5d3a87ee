import OpenAI from "openai";
import { BaseWordEntry, BaseWordInContextEntry, PartOfSpeech, StreamingWordInContextEntry } from "../../types";
import { definitionForLanguagePrompt, listAllMeaningPrompt, meaningInContextJsonPrompt, meaningInContextShortPrompt, meaningInContextShortForLanguagePrompt, moreExamplesPrompt, moreSynonymsPrompt } from "./prompt";
import { meaningInContextMarkdownPrompt } from "../gemini/prompt";
import { parseMarkdownMeaningInContext, parsePartialMarkdownMeaningInContext } from "../../utils/markdownParser";
import { DefinitionForLanguagePromptResult, DictionarySource, ExamplePromptResult, SynonymPromptResult } from "../types";
import { jsonrepair } from "jsonrepair";
import { correctLLMResponse } from "../utils";

export class NvidiaNimSource implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 8192;
  isThinkingModel = false;
  openAI: OpenAI;

  constructor(config: { 
    modelId: number, 
    modelName: string, 
    maxToken?: number, 
    apiKey?: string,
    isThinkingModel?: boolean 
  }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.isThinkingModel = config.isThinkingModel ?? false;
    this.openAI = new OpenAI({ 
      apiKey: this.apiKey, 
      baseURL: 'https://integrate.api.nvidia.com/v1',
      dangerouslyAllowBrowser: true 
    });
  }

  private shouldAddNothinkPrefix(): boolean {
    return !this.isThinkingModel && this.modelName.toLowerCase().includes('qwen-3');
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {

    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'system', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${listAllMeaningPrompt}` : listAllMeaningPrompt 
        },
        { role: 'user', content: word }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const message = completion.choices[0].message.content;
    console.log(`NvidiaNim-${this.modelName}-listAllMeanings`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    const { definitions } = JSON.parse(jsonrepair(fixed));
    const data: BaseWordEntry = {
      word,
      llm_model: this.modelId,
      definitions: definitions.map((d: { partOfSpeech: PartOfSpeech, definition: string, example: string, context?: string }) => ({
        partOfSpeech: d.partOfSpeech,
        definition: d.definition,
        context: d.context,
        examples: [d.example],
        synonyms: []
      }))
    };
    return data;
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'system', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${definitionForLanguagePrompt(language)}` : definitionForLanguagePrompt(language) 
        },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const message = completion.choices[0].message.content;
    console.log(`NvidiaNim-${this.modelName}-listAllMeaningsForLanguage`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    const { definitions } = JSON.parse(jsonrepair(fixed));
    return definitions;
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'system', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${moreExamplesPrompt}` : moreExamplesPrompt 
        },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const message = completion.choices[0].message.content;
    console.log(`NvidiaNim-${this.modelName}-getMoreExamples`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    const { result } = JSON.parse(jsonrepair(fixed));
    return result;
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'system', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${moreSynonymsPrompt}` : moreSynonymsPrompt 
        },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const message = completion.choices[0].message.content;
    console.log(`NvidiaNim-${this.modelName}-getMoreSynonymsForDefinition`, message);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    const { result } = JSON.parse(jsonrepair(fixed));
    return result;
  }

  async getMeaningInContextJson(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'user', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${meaningInContextJsonPrompt(word, context)}` : meaningInContextJsonPrompt(word, context) 
        },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`NvidiaNim-${this.modelName}-getMeaningInContextJson`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    const result = JSON.parse(jsonrepair(fixed));
    return {
      word,
      context,
      offset,
      definition: result,
      llm_model: this.modelId,
    };
  }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'user', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${meaningInContextMarkdownPrompt(word, context)}` : meaningInContextMarkdownPrompt(word, context) 
        },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`NvidiaNim-${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');

    try {
      const parsedDefinition = parseMarkdownMeaningInContext(message);
      return {
        word,
        context,
        offset,
        definition: parsedDefinition,
        llm_model: this.modelId,
      };
    } catch (err) {
      throw new Error('Cannot parse markdown response', { cause: err });
    }
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'user', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${meaningInContextShortPrompt(word, context)}` : meaningInContextShortPrompt(word, context) 
        },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`NvidiaNim-${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);

    return {
      word,
      context,
      offset,
      definitionShort: {
        explanation: fixed,
      },
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string
  ): Promise<string> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'user', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${meaningInContextShortForLanguagePrompt(word, context, language)}` : meaningInContextShortForLanguagePrompt(word, context, language) 
        },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`NvidiaNim-${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    const fixed = correctLLMResponse(message);
    return fixed;
  }

  async *getMeaningInContextShortStream(word: string, context: string, offset: number): AsyncGenerator<{ chunk: string; isComplete: boolean; result?: BaseWordInContextEntry }> {
    // Stream the response
    const stream = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'user', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${meaningInContextShortPrompt(word, context)}` : meaningInContextShortPrompt(word, context) 
        },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
      stream: true,
    });

    let fullText = '';
    
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        fullText += content;
        
        yield {
          chunk: content,
          isComplete: false
        };
      }
    }
    
    console.log(`NvidiaNim-${this.modelName}-getMeaningInContextShortStream complete:`, fullText);
    
    // Apply the same text correction as the non-streaming version
    const fixed = correctLLMResponse(fullText);
    
    // Yield final result
    yield {
      chunk: '',
      isComplete: true,
      result: {
        word,
        context,
        offset,
        definitionShort: {
          explanation: fixed,
        },
        llm_model: this.modelId,
      }
    };
  }

  async *getMeaningInContextStream(word: string, context: string, offset: number): AsyncGenerator<{ chunk: string; isComplete: boolean; result?: StreamingWordInContextEntry }> {
    // Use detailed markdown prompt
    const prompt = meaningInContextMarkdownPrompt(word, context);
    
    // Stream markdown response
    const stream = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'user', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${prompt}` : prompt 
        },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
      stream: true,
    });
    
    let fullMarkdown = '';
    
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        fullMarkdown += content;
        
        // Parse incomplete markdown at each chunk
        const parsedDefinition = parsePartialMarkdownMeaningInContext(fullMarkdown);
        
        yield {
          chunk: content,
          isComplete: false,
          result: {
            word,
            context,
            offset,
            definition: Object.keys(parsedDefinition).length > 0 ? parsedDefinition : undefined,
            llm_model: this.modelId,
          }
        };
      }
    }
    
    console.log(`NvidiaNim-${this.modelName}-getMeaningInContextStream complete:`, fullMarkdown);
    
    // Final result with complete parsing
    const finalParsedDefinition = parseMarkdownMeaningInContext(fullMarkdown);
    yield {
      chunk: '',
      isComplete: true,
      result: {
        word,
        context,
        offset,
        definition: finalParsedDefinition,
        llm_model: this.modelId,
      }
    };
  }

  async *getMeaningInContextShortForLanguageStream(
    word: string,
    context: string,
    offset: number,
    language: string,
  ): AsyncGenerator<{ chunk: string; isComplete: boolean; result?: string }> {
    // Stream the response
    const stream = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { 
          role: 'user', 
          content: this.shouldAddNothinkPrefix() ? `/nothink\n${meaningInContextShortForLanguagePrompt(word, context, language)}` : meaningInContextShortForLanguagePrompt(word, context, language) 
        },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
      stream: true,
    });

    let fullText = '';
    
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        fullText += content;
        
        yield {
          chunk: content,
          isComplete: false
        };
      }
    }
    
    console.log(`NvidiaNim-${this.modelName}-getMeaningInContextShortForLanguageStream complete:`, fullText);
    
    // Apply the same text correction as the non-streaming version
    const fixed = correctLLMResponse(fullText);
    
    // Yield final result
    yield {
      chunk: '',
      isComplete: true,
      result: fixed
    };
  }
}