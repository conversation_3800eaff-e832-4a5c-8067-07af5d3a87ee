export const listAllMeaningPrompt = `\
Provide all definitions of the given word or phrase in a JSON format. For each definition, include the following keys:

"partOfSpeech": The part of speech (e.g., noun, verb, adjective, idiom, phrase, conjunction, etc.).
"definition": The definition or meaning of the word or phrase.
"context": The detailed context of use for this definition.
"example": An example sentence demonstrating usage.

Example input:
merit

Expected output:
{
  "definitions": [
    {
      "partOfSpeech": "noun",
      "definition": "a quality or feature that deserves praise, reward or esteem",
      "context": "Is commonly used in contexts where someone or something is being judged or evaluated for their qualities or accomplishments. It emphasizes that a particular quality or achievement deserves recognition, praise, or reward. For example, in formal settings like academia, professional evaluations, or awards, people might refer to the "merit" of a project or individual's work.",
      "example": "The merit of her work was recognized with a prestigious award."
    },
    {
      "partOfSpeech": "verb",
      "definition": "to deserve or be worthy of",
      "context": "Appears when actions or efforts are considered deserving of a particular response or reward. This usage is often found in discussions about personal achievements or workplace evaluations, highlighting whether someone's efforts are worthy of recognition",
      "example": "His hard work merited a promotion."
    }
  ]
}`;

export const definitionForLanguagePrompt = (language: string) => (`
I will provide a list of words or phrases along with their part of speech, meaning, usage context and an example usage.
Your task is to generate a definition and usage context for each meaning (not an example usage) in ${language} language.
Please try your best to find most appropriate corresponding words in ${language} for the definitions,
rather than simply translating the definition from English to ${language}. Your response should strictly follow the
following json format:

{
  "definitions": [
    {
      "word": "...", (most appropriate corresponding words or phrases in ${language} language)
      "definition": "...", (most appropriate corresponding definition in ${language} language)
      "context" "..." (the usage context of the word/phrase in ${language} language)
    },
    {
      "word": "...", (most appropriate corresponding words or phrases in ${language} language)
      "definition": "...", (most appropriate corresponding definition in ${language} language)
      "context" "..." (the usage context of the word/phrase in ${language} language)
    },
    ...
  ]
}

Note: The order of the json objects should follow the order of the input list. Don't include any further explanation in your response.`
);

export const moreExamplesPrompt = `\
I will provide a list of words or phrases along with their part of speech, meaning, usage context and an example usage.
Your task is to generate 4 additional example usages for each meaning in the following JSON format:

{
  "result": [
    {
      "examples": [
        "..." (4 new example usages to be generated for the first word/phrase),
        "...",
        "...",
        "..."
      ]
    },
    {
      "examples": [
        "...",
        "...",
        "...",
        "..."
      ]
    },
    ...
  ]
}

Note: The order of the JSON objects should follow the order of the input list. Don't include any further explanation in your response.`;

export const moreSynonymsPrompt = `\
I will provide a word or phrase along with its part of speech, meaning, usage context and an example usage.
Your task is to generate 4 additional synonyms, each with a brief usage example to illustrate any nuanced differences.

{
  "result": [
    {
      "synonym": "...",
      "example": "..."
    },
    {
      "synonym": "...",
      "example": "..."
    },
    ...
  ]
}

Don't include any further explanation in your response.`;

export const meaningInContextPrompt = (word: string, context: string) => (`\
You will receive a passage followed by a question about the meaning of a specific word or phrase within the passage. Please provide the following:

- A meaning(definition) of the word or phrase in the passage
- A detailed explanation of the meaning of the word or phrase as it is used in the passage, including its context and nuances.
- Give more examples of this usage with short explanation.
- Alternative words or phrases (synonyms) that can be used with the same meaning in a similar context, with brief explanations and examples of any differences in tone or usage.

Passage:
${context}

Word: ${word}
`);

export const meaningInContextToJson = (text: string) => (`
Convert the following text into a json format:
${text}

The json format should be:
{
  "definition": "",
  "partOfSpeech": "",
  "explanation": "",
  "examples": [
    {
    "example": "",
    "explanation": ""
    },
    // ... more examples
  ],
  "synonyms": [
    {
      "synonym": "",
      "example: "",
      "explanation": ""
    },
    // ... more synonyms
  ]
}

Note:
- If you cannot get the field from the text, please try to guess it (don't leave the field empty).
- The synonyms should be in lowercase if possible, unless they are proper nouns or special names.
- Don't include any further explanation or wrap the json inside code block in your response
  such as including text like "Here is the converted JSON format:"
`);

export const meaningInContextJsonPrompt = (word: string, context: string) => (`\
You will receive a passage followed by a question about the meaning of a specific word or phrase within the passage. Please provide the following:

- A meaning(definition) of the word or phrase in the passage
- A detailed explanation of the meaning of the word or phrase as it is used in the passage, including its context and nuances.
- Give more examples of this usage with short explanation.
- Alternative words or phrases (synonyms) that can be used with the same meaning in a similar context, with brief explanations and examples of any differences in tone or usage.

Your response should be in the following JSON format:
{
  "definition": "",
  "partOfSpeech": "",
  "explanation": "",
  "examples": [
    {
    "example": "",
    "explanation": ""
    },
  ],
  "synonyms": [
    {
      "synonym": "",
      "example: "",
      "explanation": ""
    },
  ]
}

Passage:
${context}

Word: ${word}
`);

export const meaningInContextShortPrompt = (word: string, context: string) => (`\
What does "${word}" mean in the following context? Please keep it short (less than 3 or 4 sentences if possible)
and easy to understand for a non-native English speaker.

Context: ${context}
`);

export const meaningInContextShortForLanguagePrompt = (word: string, context: string, language: string) => (`\
What does "${word}" mean in the following context? Please explain in ${language} and keep it short (less than 3 or 4 sentences if possible)

Context: ${context}
`);