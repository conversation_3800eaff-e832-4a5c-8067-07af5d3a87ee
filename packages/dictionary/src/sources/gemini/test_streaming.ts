import { GeminiSource } from './gemini';

const gemini = new GeminiSource({
  modelName: 'gemma-3-4b-it',
  modelId: 0,
  maxToken: 1000,
  apiKey: 'AIzaSyAG_Lq30NrMK6ICjFi4nO7JOrCO-202Oms'
});

async function testGetMeaningInContextStream() {
  const word = 'meticulous';
  const context = '<PERSON> spent hours preparing for the presentation, being meticulous about every detail. She checked each slide multiple times, verified all the data, and rehearsed her delivery until it was perfect. Her meticulous approach ensured that nothing would go wrong during the important meeting.';
  const offset = 60;

  try {
    const generator = gemini.getMeaningInContextStream(word, context, offset);
    
    for await (const chunk of generator) {
      console.log(JSON.stringify(chunk.result, null, 2));
      
      if (chunk.isComplete) {
        break;
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

async function runAllStreamingTests() {
  await testGetMeaningInContextStream();
}

runAllStreamingTests().catch(error => {
  console.error('Error:', error);
});