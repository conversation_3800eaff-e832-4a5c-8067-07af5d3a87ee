import { ref, computed } from 'vue';
import { Dictionary, type DictionarySource, type WordInContextEntry, type BaseWordInContextEntry } from 'pickvocab-dictionary';
import { RemoteWordInContextApi } from '~/api/wordInContext';
import { reduceContext } from '~/utils/contextCard';
import { useLLMStore } from '~/stores/llm';
import { useAppStore } from '~/stores/app';

export interface LookupOptions {
  word: string;
  context: string;
  offset: number;
  isDetailed?: boolean;
  language?: string;
}

export interface LookupResult {
  wordEntry: Ref<WordInContextEntry | undefined>;
  isLoading: Ref<boolean>;
  isStreamingSimple: Ref<boolean>;
  isStreamingDetailed: Ref<boolean>;
  errorMessage: Ref<string>;
  lookup: (options: LookupOptions) => Promise<void>;
  refresh: (language?: string, isDetailedMode?: boolean) => Promise<void>;
  clear: () => void;
}

export function useDictionaryLookup(): LookupResult {
  const wordEntry = ref<WordInContextEntry | undefined>(undefined);
  const isLoading = ref(false);
  const isStreamingSimple = ref(false);
  const isStreamingDetailed = ref(false);
  const errorMessage = ref('');
  
  // Request tracking to prevent race conditions
  let currentRequestId = 0;
  
  const api = new RemoteWordInContextApi();
  const llmStore = useLLMStore();
  const appStore = useAppStore();
  
  const dictionary = computed(() => {
    let sources: DictionarySource[] = [llmStore.pickvocabDictionarySource];
    if (llmStore.activeUserModel) {
      sources = [llmStore.createDictionarySource(llmStore.activeUserModel), ...sources];
    }
    return new Dictionary(sources);
  });

  async function performStreamingLookup(
    word: string,
    context: string,
    offset: number,
    tempId: number,
    requestId: number
  ): Promise<BaseWordInContextEntry | null> {
    try {
      isStreamingSimple.value = true;
      
      // Create a temporary wordEntry with empty explanation
      wordEntry.value = {
        word,
        context,
        offset,
        definitionShort: {
          explanation: '',
        },
        llm_model: (dictionary.value.sources[0] as any).modelId || 0,
        id: tempId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      } as WordInContextEntry;
      
      isLoading.value = false;

      // Stream the response
      const generator = dictionary.value.getMeaningInContextShortStream(word, context, offset);
      
      for await (const chunk of generator) {
        // Check if this request is still current
        if (currentRequestId !== requestId) {
          console.log('Streaming request cancelled (new request started)');
          return null;
        }
        
        if (chunk.isComplete) {
          // Final result received
          if (currentRequestId === requestId) {
            isStreamingSimple.value = false;
          }
          return chunk.result!;
        } else {
          // Stream chunk received - append to explanation
          if (wordEntry.value && wordEntry.value.definitionShort && currentRequestId === requestId) {
            wordEntry.value.definitionShort.explanation += chunk.chunk;
          }
        }
      }
      
      return null;
    } catch (streamErr) {
      console.log('Streaming failed, falling back to non-streaming:', streamErr);
      if (currentRequestId === requestId) {
        isStreamingSimple.value = false;
      }
      throw streamErr;
    }
  }

  async function performDetailedStreamingLookup(
    word: string,
    context: string,
    offset: number,
    tempId: number,
    requestId: number
  ): Promise<BaseWordInContextEntry | null> {
    try {
      isStreamingDetailed.value = true;
      
      // Create a temporary wordEntry with empty definition
      wordEntry.value = {
        word,
        context,
        offset,
        definition: {
          partOfSpeech: '',
          definition: '',
          explanation: '',
          examples: [],
          synonyms: []
        },
        llm_model: (dictionary.value.sources[0] as any).modelId || 0,
        id: tempId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      } as WordInContextEntry;
      
      isLoading.value = false;

      // Stream the response
      const generator = dictionary.value.getMeaningInContextStream(word, context, offset);
      
      for await (const chunk of generator) {
        // Check if this request is still current
        if (currentRequestId !== requestId) {
          console.log('Detailed streaming request cancelled (new request started)');
          return null;
        }
        
        if (chunk.isComplete) {
          // Final result received
          if (currentRequestId === requestId) {
            isStreamingDetailed.value = false;
          }
          return chunk.result as BaseWordInContextEntry;
        } else {
          // Stream chunk received - update with partial result
          if (wordEntry.value && chunk.result && currentRequestId === requestId) {
            wordEntry.value = {
              ...wordEntry.value,
              ...chunk.result,
              id: tempId,
              createdAt: wordEntry.value.createdAt,
              updatedAt: wordEntry.value.updatedAt
            } as WordInContextEntry;
          }
        }
      }
      
      return null;
    } catch (streamErr) {
      console.log('Detailed streaming failed, falling back to non-streaming:', streamErr);
      if (currentRequestId === requestId) {
        isStreamingDetailed.value = false;
      }
      throw streamErr;
    }
  }

  async function performNonStreamingLookup(
    word: string,
    context: string, 
    offset: number,
    isDetailed: boolean
  ): Promise<BaseWordInContextEntry> {
    if (isDetailed) {
      return await dictionary.value.getMeaningInContext(word, context, offset);
    } else {
      return await dictionary.value.getMeaningInContextShort(word, context, offset);
    }
  }

  async function saveToServer(entry: BaseWordInContextEntry): Promise<WordInContextEntry | null> {
    try {
      const serverEntry = await api.create(entry);
      return serverEntry;
    } catch (err) {
      console.error('Failed to save to server:', err);
      return null;
    }
  }

  async function updateOnServer(entry: WordInContextEntry): Promise<WordInContextEntry | null> {
    try {
      const serverEntry = await api.put(entry);
      return serverEntry;
    } catch (err) {
      console.error('Failed to update on server:', err);
      return null;
    }
  }

  async function lookup(options: LookupOptions): Promise<void> {
    const { word, context, offset, isDetailed = false } = options;
    
    try {
      // Increment request ID to cancel any ongoing requests
      currentRequestId++;
      const requestId = currentRequestId;
      
      wordEntry.value = undefined;
      isLoading.value = true;
      errorMessage.value = '';
      
      // Use reducedContext for lookup
      const reduced = reduceContext(context, word, offset, 3);
      const tempId = -Date.now(); // Negative timestamp as temporary ID
      
      let base: BaseWordInContextEntry | null = null;
      
      if (isDetailed) {
        // Try streaming for detailed meaning
        try {
          base = await performDetailedStreamingLookup(reduced.selectedText, reduced.text, reduced.offset, tempId, requestId);
          
          if (base && currentRequestId === requestId) {
            wordEntry.value = {
              ...base,
              id: tempId,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            } as WordInContextEntry;
          }
        } catch (streamErr) {
          // Fall back to non-streaming only if this is still the current request
          if (currentRequestId === requestId) {
            base = await performNonStreamingLookup(reduced.selectedText, reduced.text, reduced.offset, true);
            
            // Create a temporary local wordEntry immediately
            wordEntry.value = {
              ...base,
              id: tempId,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            } as WordInContextEntry;
            
            isLoading.value = false;
          }
        }
      } else {
        // Try streaming for short meaning
        try {
          base = await performStreamingLookup(reduced.selectedText, reduced.text, reduced.offset, tempId, requestId);
          
          if (base && currentRequestId === requestId) {
            wordEntry.value = {
              ...base,
              id: tempId,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            } as WordInContextEntry;
          }
        } catch (streamErr) {
          // Fall back to non-streaming only if this is still the current request
          if (currentRequestId === requestId) {
            base = await performNonStreamingLookup(reduced.selectedText, reduced.text, reduced.offset, false);
            
            // Create a temporary local wordEntry immediately
            wordEntry.value = {
              ...base,
              id: tempId,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            } as WordInContextEntry;
            
            isLoading.value = false;
          }
        }
      }
      
      // Save to server in the background only if this is still the current request
      if (base && currentRequestId === requestId) {
        saveToServer(base).then(serverEntry => {
          if (serverEntry && currentRequestId === requestId) {
            wordEntry.value = serverEntry;
          }
        });
      }
      
    } catch (err) {
      // Only handle error if this is still the current request
      if (currentRequestId === currentRequestId) {
        console.log(err);
        errorMessage.value = `${err}`;
        isLoading.value = false;
        isStreamingSimple.value = false;
        isStreamingDetailed.value = false;
      }
    }
  }

  async function refresh(language = 'English', isDetailedMode?: boolean): Promise<void> {
    try {
      const oldEntry = wordEntry.value;
      if (!oldEntry || !oldEntry.word || !oldEntry.context || oldEntry.offset === undefined) {
        console.error('Cannot refresh: missing word entry data');
        return;
      }
      
      wordEntry.value = undefined;
      isLoading.value = true;
      errorMessage.value = '';

      let base: BaseWordInContextEntry | null = null;
      
      if (language === 'English') {
        // Use reducedContext for refresh
        const reduced = reduceContext(oldEntry.context, oldEntry.word, oldEntry.offset, 3);
        
        // Use passed isDetailedMode parameter or fall back to appStore
        const useDetailedMode = isDetailedMode !== undefined ? isDetailedMode : appStore.isShowWordContextDetailed;
        
        if (useDetailedMode) {
          // Try streaming for detailed meaning
          try {
            // Create a temporary wordEntry with empty definition
            wordEntry.value = {
              ...oldEntry,
              definition: {
                partOfSpeech: '',
                definition: '',
                explanation: '',
                examples: [],
                synonyms: []
              },
            };
            
            isLoading.value = false;
            isStreamingDetailed.value = true;

            // Stream the response
            const generator = dictionary.value.getMeaningInContextStream(reduced.selectedText, reduced.text, reduced.offset);
            const requestId = currentRequestId;
            
            for await (const chunk of generator) {
              // Check if this request is still current
              if (currentRequestId !== requestId) {
                console.log('Refresh detailed streaming request cancelled');
                return;
              }
              
              if (chunk.isComplete) {
                // Final result received
                if (currentRequestId === requestId) {
                  base = chunk.result as BaseWordInContextEntry;
                  wordEntry.value = {
                    ...oldEntry,
                    ...base,
                  };
                  isStreamingDetailed.value = false;
                }
                break;
              } else {
                // Stream chunk received - update with partial result
                if (wordEntry.value && chunk.result && currentRequestId === requestId) {
                  wordEntry.value = {
                    ...wordEntry.value,
                    ...chunk.result,
                    id: oldEntry.id,
                    createdAt: oldEntry.createdAt,
                    updatedAt: oldEntry.updatedAt
                  } as WordInContextEntry;
                }
              }
            }
          } catch (streamErr) {
            console.log('Detailed streaming failed, falling back to non-streaming:', streamErr);
            isStreamingDetailed.value = false;
            // Fall back to non-streaming
            base = await dictionary.value.getMeaningInContext(
              reduced.selectedText,
              reduced.text,
              reduced.offset,
            );
            
            wordEntry.value = {
              ...oldEntry,
              ...base,
            };
            isLoading.value = false;
          }
        } else {
          // Try streaming for short meaning
          try {
            // Create a temporary wordEntry with empty explanation
            wordEntry.value = {
              ...oldEntry,
              definitionShort: {
                explanation: '',
              },
            };
            
            isLoading.value = false;
            isStreamingSimple.value = true;

            // Stream the response
            const generator = dictionary.value.getMeaningInContextShortStream(reduced.selectedText, reduced.text, reduced.offset);
            const requestId = currentRequestId;
            
            for await (const chunk of generator) {
              // Check if this request is still current
              if (currentRequestId !== requestId) {
                console.log('Refresh short streaming request cancelled');
                return;
              }
              
              if (chunk.isComplete) {
                // Final result received
                if (currentRequestId === requestId) {
                  base = chunk.result!;
                  wordEntry.value = {
                    ...oldEntry,
                    ...base,
                  };
                  isStreamingSimple.value = false;
                }
                break;
              } else {
                // Stream chunk received - append to explanation
                if (wordEntry.value && wordEntry.value.definitionShort && currentRequestId === requestId) {
                  wordEntry.value.definitionShort.explanation += chunk.chunk;
                }
              }
            }
          } catch (streamErr) {
            console.log('Streaming failed, falling back to non-streaming:', streamErr);
            isStreamingSimple.value = false;
            // Fall back to non-streaming
            base = await dictionary.value.getMeaningInContextShort(
              reduced.selectedText,
              reduced.text,
              reduced.offset,
            );
            
            wordEntry.value = {
              ...oldEntry,
              ...base,
            };
            isLoading.value = false;
          }
        }
      } else {
        const useDetailedMode = isDetailedMode !== undefined ? isDetailedMode : appStore.isShowWordContextDetailed;
        if (useDetailedMode) throw new Error('Unsupported');
        
        // Try streaming for language lookup
        try {
          // Create a temporary wordEntry with empty explanation for the language
          wordEntry.value = {
            ...oldEntry,
            definitionShort: {
              ...oldEntry.definitionShort!,
              languages: {
                ...oldEntry.definitionShort?.languages,
                [language]: {
                  explanation: '',
                }
              }
            }
          };
          
          isLoading.value = false;
          isStreamingSimple.value = true;

          // Stream the response for language
          const generator = dictionary.value.getMeaningInContextShortForLanguageStream(oldEntry, language);
          const requestId = currentRequestId;
          
          for await (const chunk of generator) {
            // Check if this request is still current
            if (currentRequestId !== requestId) {
              console.log('Refresh language streaming request cancelled');
              return;
            }
            
            if (chunk.isComplete) {
              // Final result received
              if (currentRequestId === requestId) {
                wordEntry.value = chunk.result!;
                isStreamingSimple.value = false;
              }
              break;
            } else {
              // Stream chunk received - append to explanation
              if (wordEntry.value && wordEntry.value.definitionShort?.languages?.[language] && currentRequestId === requestId) {
                wordEntry.value.definitionShort.languages[language].explanation += chunk.chunk;
              }
            }
          }
        } catch (streamErr) {
          console.log('Language streaming failed, falling back to non-streaming:', streamErr);
          isStreamingSimple.value = false;
          // Fall back to non-streaming
          const result = await dictionary.value.getMeaningInContextShortForLanguage(oldEntry, language);
          wordEntry.value = result;
          isLoading.value = false;
        }
      }
      
      // Save to server in the background
      if (wordEntry.value) {
        if (base) {
          // For English language updates
          const newEntry = {
            ...oldEntry,
            ...base,
          };
          updateOnServer(newEntry).then(serverEntry => {
            if (serverEntry) {
              wordEntry.value = serverEntry;
            }
          });
        } else {
          // For other language updates (base is null since we handled it in streaming)
          updateOnServer(wordEntry.value).then(serverEntry => {
            if (serverEntry) {
              wordEntry.value = serverEntry;
            }
          });
        }
      }
      
    } catch (err) {
      console.log(err);
      errorMessage.value = `${err}`;
      isLoading.value = false;
      isStreamingSimple.value = false;
      isStreamingDetailed.value = false;
    }
  }

  function clear(): void {
    // Increment request ID to cancel any ongoing requests
    currentRequestId++;
    
    wordEntry.value = undefined;
    isLoading.value = false;
    isStreamingSimple.value = false;
    isStreamingDetailed.value = false;
    errorMessage.value = '';
  }

  return {
    wordEntry,
    isLoading,
    isStreamingSimple,
    isStreamingDetailed,
    errorMessage,
    lookup,
    refresh,
    clear
  };
}