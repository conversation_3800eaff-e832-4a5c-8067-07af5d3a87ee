import { useQuery } from "@tanstack/vue-query";
import { RemoteGenericCardsApi } from "~/api/genericCard";

export function useCardDecksQuery(cardId: Ref<CardId>) {
  return useQuery({
    queryKey: ['card-decks', cardId],
    queryFn: async () => {
      const api = new RemoteGenericCardsApi();
      return await api.getCardDecks(cardId.value);
    },
    enabled: computed(() => !!cardId.value),
    staleTime: Infinity, // Never automatically stale - we control refetching manually via invalidation
  });
}