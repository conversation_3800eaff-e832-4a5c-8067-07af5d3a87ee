<template>
  <span
    :class="[
      'shiny-text',
      { 'disabled': disabled },
      className
    ]"
    :style="{ animationDuration: `${speed}s` }"
  >
    {{ text }}
  </span>
</template>

<script setup lang="ts">
defineProps({
  text: {
    type: String,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  speed: {
    type: Number,
    default: 2
  },
  className: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.shiny-text {
  color: #6b7280;
  background: linear-gradient(
    120deg,
    rgba(107, 114, 128, 0.9) 40%,
    rgba(255, 255, 255, 1) 50%,
    rgba(107, 114, 128, 0.9) 60%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
  animation: shine 2s linear infinite;
}

@keyframes shine {
  0% {
    background-position: 100%;
  }
  100% {
    background-position: -100%;
  }
}

.shiny-text.disabled {
  animation: none;
}
</style>