export let simpleRequestId = 0;

export function nextSimpleRequestId(): number {
  return ++simpleRequestId;
}

export function currentSimpleRequestId(): number {
  return simpleRequestId;
}

export let languageRequestId = 0;
export const activeLanguageRequests: Record<string, string> = {};

export function nextLanguageRequestId(language: string): string {
  const id = `${++languageRequestId}-${language}`;
  activeLanguageRequests[language] = id;
  return id;
}

export function currentLanguageRequestId(language: string): string | undefined {
  return activeLanguageRequests[language];
}

export let detailedRequestId = 0;

export function nextDetailedRequestId(): number {
  return ++detailedRequestId;
}

export function currentDetailedRequestId(): number {
  return detailedRequestId;
} 