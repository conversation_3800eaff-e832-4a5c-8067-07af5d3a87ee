import { ref, type Ref } from 'vue';
import { useRouter } from 'vue-router';
import { useEventBus } from '@vueuse/core';
import type { BaseWordInContextEntry, WordInContextEntry } from 'pickvocab-dictionary';
import type { LookupEvent } from './events';
import { RemoteWordInContextApi } from '~/api/wordInContext';

/**
 * Handles creation / update of `WordInContextEntry` on the server and keeps the
 * browser URL in sync with the resolved (real) ID.
 *
 * Behaviour:
 * 1. When the very first stream for a new word completes the entry only has a
 *    temporary ID ("temp-123"). The manager creates the entry on the server,
 *    receives the real ID and replaces the temporary ID locally and in the URL.
 * 2. While the real ID is being resolved any additional stream completions set
 *    a `pendingServerUpdate` flag so that we perform **one** consolidated PUT
 *    request after the create finishes.
 * 3. Once the real ID exists every further stream completion triggers a simple
 *    PUT update to keep the server state fresh.
 */
export function useIdManager(wordEntry: Ref<WordInContextEntry | undefined>, options?: { updateUrl?: boolean }) {
  const router = useRouter();
  const api = new RemoteWordInContextApi();

  const idResolutionInProgress = ref(false);
  const pendingServerUpdate = ref(false);

  const bus = useEventBus<LookupEvent>('lookup-events');

  bus.on(async (event) => {
    if (event.type !== 'StreamCompleted') return;

    const entry = wordEntry.value;
    if (!entry) return;

    const hasRealId = typeof entry.id === 'number';

    // --- CASE 1: Entry already has a real ID — just PUT update ----------------
    if (hasRealId) {
      try {
        await api.put(entry);
      } catch (e) {
        console.error('[IDManager] Failed to update entry', e);
      }
      return;
    }

    // --- CASE 2: Entry still has a temporary ID --------------------------------

    // If another create is already running, just mark that we need an update
    // afterwards and return early.
    if (idResolutionInProgress.value) {
      pendingServerUpdate.value = true;
      return;
    }

    idResolutionInProgress.value = true;

    try {
      const base: BaseWordInContextEntry = {
        word: entry.word,
        context: entry.context,
        offset: entry.offset,
        definitionShort: entry.definitionShort,
        definition: entry.definition,
        llm_model: entry.llm_model,
      };

      const created = await api.create(base);

      // Replace the local reactive state with the server-authoritative version.
      wordEntry.value = { ...created };

      // Update the URL in the main ContextView2 if requested.
      if (options?.updateUrl) {
        router.replace(`/app/contextual-meaning/${created.id}`);
      }

      // If we queued an update while creating, send it now.
      if (pendingServerUpdate.value) {
        pendingServerUpdate.value = false;
        await api.put(created);
      }
    } catch (e) {
      console.error('[IDManager] Failed to create entry', e);
    } finally {
      idResolutionInProgress.value = false;
    }
  });
} 