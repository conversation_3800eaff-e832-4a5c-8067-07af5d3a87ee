import type { WordInContextEntry, BaseWordInContextDefinition } from 'pickvocab-dictionary';

export type StreamType = 'simple' | 'detailed';
export type Language = 'English' | 'Spanish' | 'French' | string; // Allow other strings

export interface StreamStartedEvent {
  type: 'StreamStarted';
  streamType: StreamType;
  language?: Language;
  requestDetails: {
    word: string;
    context: string;
    offset: number;
  };
}

export interface SimpleTextChunkEvent {
  type: 'SimpleTextChunk';
  language: Language;
  chunk: string;
  requestId: string | number;
}

export interface DetailedPartialUpdateEvent {
  type: 'DetailedPartialUpdate';
  definition: Partial<BaseWordInContextDefinition>;
  requestId: string | number;
}

export interface StreamCompletedEvent {
  type: 'StreamCompleted';
  streamType: StreamType;
  language?: Language;
  requestId: string | number;
}

export interface StreamErrorEvent {
  type: 'StreamError';
  streamType: StreamType;
  language?: Language;
  error: string;
  requestId: string | number;
}

export type LookupEvent =
  | StreamStartedEvent
  | SimpleTextChunkEvent
  | DetailedPartialUpdateEvent
  | StreamCompletedEvent
  | StreamErrorEvent;