<script setup lang="ts">
// @ts-ignore
import IconSearch from '@tabler/icons-vue/dist/esm/icons/IconSearch.mjs';
import TiptapEditor from '~/components/editor/TiptapEditor.vue';
import type { WordInContextEntry } from 'pickvocab-dictionary';
import DictionaryWordViewErrorAlert from '~/components/app/dictionary/DictionaryWordViewErrorAlert.vue';
import DictionaryWordViewInfoAlert from '~/components/app/dictionary/DictionaryWordViewInfoAlert.vue';
import ExplanationView from '~/components/app/contextualMeaning/ExplanationView.vue';
import { useDictionaryLookup2 } from './useDictionaryLookup2';
import { RemoteWordInContextApi } from '~/api/wordInContext';
import { useCreateContextCard } from '~/composables/useCardMutations';
import type { BaseContextCard } from '~/utils/card';

const store = useAppStore();
const llmStore = useLLMStore();
const route = useRoute();
const router = useRouter();

// TanStack Query mutation for proper cache management
const { mutate: createContextCardMutation } = useCreateContextCard();

// New composable for dictionary lookup
const {
  wordEntry,
  isLoading,
  isStreamingSimple,
  isStreamingDetailed,
  errorMessage,
  activeSimpleLanguages,
  lookupSimpleEnglish,
  lookupSimpleLanguage,
  lookupDetailed,
} = useDictionaryLookup2({ updateUrl: true });

const selected = ref<{ selectedText: string, offset: number, text: string } | undefined>(undefined);
const selectedSimpleViewLanguage = ref<string | undefined>('English');

/**
 * Computed flag: `true` only when the currently selected simple-view language
 * is *actively* streaming. This prevents the streaming indicator from showing
 * on English (or any other language) when a different language stream is the
 * one in progress.
 */
const isStreamingSimpleCurrentLang = computed(() => {
  // If there is no simple stream at all, we can shortcut.
  if (!isStreamingSimple.value) return false;

  // The dropdown defaults to English if undefined.
  const lang = selectedSimpleViewLanguage.value || 'English';

  // activeSimpleLanguages is a Set wrapped in a ref.
  return activeSimpleLanguages.value.has(lang);
});

/**
 * Ensure we have a simple definition for the given language. Triggers the
 * appropriate stream only if the explanation is currently missing.
 */
function ensureSimpleDefinition(lang: string) {
  if (!wordEntry.value) return;

  if (lang === 'English') {
    if (!wordEntry.value.definitionShort?.explanation) {
      lookupSimpleEnglish(wordEntry.value.word, wordEntry.value.context, wordEntry.value.offset);
    }
    return;
  }

  const hasExplanation =
    wordEntry.value.definitionShort?.languages &&
    wordEntry.value.definitionShort.languages[lang] &&
    wordEntry.value.definitionShort.languages[lang].explanation;

  if (!hasExplanation) {
    void lookupSimpleLanguage(lang);
  }
}

// Trigger language-specific lookup when the dropdown changes.
watch(
  () => selectedSimpleViewLanguage.value,
  async (newLang, oldLang) => {
    if (!newLang) return;
    ensureSimpleDefinition(newLang);
  },
);

// Handle view switching between detailed and simple.
// Trigger detailed lookup when the user switches to detailed view and we don't have data yet.
watch(
  () => store.isShowWordContextDetailed,
  (newVal) => {
    if (!wordEntry.value) return;
    if (newVal) {
      // Switched to detailed view.
      if (!wordEntry.value.definition) {
        lookupDetailed();
      }
    } else {
      // Switched back to simple view.
      ensureSimpleDefinition(selectedSimpleViewLanguage.value ?? 'English');
    }
  },
);

// When a new wordEntry is loaded (e.g., via URL hydration) ensure the
// definition for the *current* view is available or kick off the
// appropriate stream.
watch(
  () => wordEntry.value,
  (newEntry) => {
    if (!newEntry) return;

    if (store.isShowWordContextDetailed) {
      // Detailed view: if definition missing, start stream.
      if (!newEntry.definition) {
        lookupDetailed();
      }
    } else {
      // Simple view: ensure explanation for current language.
      ensureSimpleDefinition(selectedSimpleViewLanguage.value ?? 'English');
    }
  },
  { immediate: true },
);

// Hydrate word entry when navigating directly via URL (Phase 8 logic moved here)
watch(
  () => route.params.id,
  async (newId) => {
    if (!newId) return;

    if (typeof newId === 'string' && newId.startsWith('temp-')) return;

    const numericId = Number(newId);
    if (Number.isNaN(numericId) || numericId <= 0) return;

    // Avoid duplicate fetch if we already have the entry.
    if (wordEntry.value && wordEntry.value.id === numericId) return;

    const api = new RemoteWordInContextApi();
    try {
      isLoading.value = true;
      const fetched = await api.get(numericId);
      if (fetched) {
        wordEntry.value = fetched;
      }
    } catch (err) {
      console.error('[ContextView2] Failed to hydrate entry', err);
      errorMessage.value = 'Failed to load entry.';
    } finally {
      isLoading.value = false;
    }
  },
  { immediate: true },
);

const title = computed(() => {
  return wordEntry.value ? `${wordEntry.value.word} - Contextual Lookup | Pickvocab` : 'Contextual Lookup | Pickvocab';
});

const description = computed(() => {
  return wordEntry.value ? `${wordEntry.value.definition?.definition || wordEntry.value.definitionShort?.explanation}. Learn more.` : 'Learn more.';
});

const llmModel = computed(() => {
  return wordEntry.value ? llmStore.getModelById(wordEntry.value?.llm_model) : undefined;
});

const showInfoMessage = computed(() => {
  const paramId = route.params.id ? Number(route.params.id) : null;
  return paramId !== 2 && llmStore.shouldShowAPIKeyAlert();
});

useSeoMeta({
  title,
  description,
  ogTitle: title,
  ogDescription: description,
  twitterTitle: title,
  twitterDescription: description,
});

// Placeholder functions - to be implemented in later phases
function handleLookup(event: { text: string, offset: number, selectedText: string }) {
  if (store.isShowWordContextDetailed) {
    // Detailed mode active — stream detailed definition only.
    lookupDetailed({ word: event.selectedText, context: event.text, offset: event.offset });
    return;
  }

  // Simple mode handling (language-specific).
  if (selectedSimpleViewLanguage.value === 'English') {
    lookupSimpleEnglish(event.selectedText, event.text, event.offset);
  } else {
    // Start a stream directly for the current language; pass word/context so the
    // helper can create a base entry if needed.
    lookupSimpleLanguage(selectedSimpleViewLanguage.value as string, {
      word: event.selectedText,
      context: event.text,
      offset: event.offset,
    });
    // Optional: also start English in background for fallback.
    // void lookupSimpleEnglish(event.selectedText, event.text, event.offset);
  }
}

function handleSelect(event: { selectedText: string, offset: number, text: string }) {
  selected.value = event;
}

function refresh(language?: string) {
  // Determine the current context (simple/detailed + language) and trigger
  // a fresh lookup for **only** that context. We intentionally rely on the
  // existing lookup helpers because they automatically increment the
  // relevant request-id counters which soft-cancel any in-flight streams.

  // If we have no word entry yet there is nothing to refresh.
  if (!wordEntry.value) return;

  // Detailed view refresh (English-only for now)
  if (store.isShowWordContextDetailed) {
    // Re-stream the detailed definition.
    lookupDetailed();
    return;
  }

  // Simple view refresh — default to the supplied language or current dropdown
  const lang = language ?? selectedSimpleViewLanguage.value ?? 'English';

  if (lang === 'English') {
    lookupSimpleEnglish(wordEntry.value.word, wordEntry.value.context, wordEntry.value.offset);
  } else {
    // For non-English we only need the language parameter; the helper will
    // reuse the existing wordEntry and create a new request-id internally.
    lookupSimpleLanguage(lang);
  }
}

function addCard(wordEntry: WordInContextEntry, callback?: () => void) {
  const baseCard: BaseContextCard = { wordInContext: wordEntry };

  createContextCardMutation(baseCard, {
    onSuccess: (card: any) => {
      // Navigate to the newly created card detail page
      router.push({ name: 'app-cards-slug-id', params: { id: card.id } });
      if (callback) callback();
    },
    onError: (error: any) => {
      if (callback) callback();
    },
  });
}

function setupApiKey() {
  store.showAPIKeyModal();
}

async function hideApiKeyAlert() {
  llmStore.lastShowAPIKeyAlert = Date.now();
}
</script>

<template>
  <div
    class="sm:ml-64 pt-24 pb-10 px-4 xl:px-24 xl:pr-32 2xl:px-24 2xl:pr-48 h-full flex flex-col bg-gray-50 overflow-auto">
    <div>
      <TiptapEditor 
        :text="wordEntry?.context"
        :selected-text="wordEntry?.word"
        :offset="wordEntry?.offset"
        @highlight="handleLookup"
        :css-classes="'tiptap !text-base md:!text-sm prose prose-sm sm:prose-base lg:prose-lg xl:prose-2xl m-5 focus:outline-none w-full min-h-[150px] max-h-[400px] overflow-auto border border-gray-200 rounded-md p-4'"
        @select="handleSelect" />
      <div v-if="!selected?.selectedText" class="mt-2 text-xs text-gray-500">Highlight word or phrase to see its exact
        meaning
        in the passage (Ctrl/Cmd + Shift + H)</div>
      <button v-else @click="handleLookup(selected)"
        class="mt-2 text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-2 py-1 text-center me-2 mb-2 flex items-center">
        <icon-search class="w-4 mr-1"></icon-search>
        <span>Lookup</span>
      </button>
    </div>

    <div v-if="errorMessage" class="mt-8">
      <DictionaryWordViewErrorAlert @retry="refresh()" @setup="store.showAPIKeyModal()" :message="errorMessage"
        :is-active-user-model="llmStore.activeUserModel ? true : false"></DictionaryWordViewErrorAlert>
    </div>

    <!-- Info Alert -->
    <DictionaryWordViewInfoAlert v-if="showInfoMessage" class="my-8" @setup="setupApiKey()"
      @dismiss="hideApiKeyAlert()">
    </DictionaryWordViewInfoAlert>

    <ExplanationView
      :word="wordEntry?.word"
      :word-entry="wordEntry"
      :llm-model="llmModel"
      :is-loading="isLoading"
      :is-streaming-simple="isStreamingSimpleCurrentLang"
      :is-streaming-detailed="isStreamingDetailed"
      v-model:is-detailed="store.isShowWordContextDetailed"
      v-model:selected-simple-view-language="selectedSimpleViewLanguage"
      @add-card="(entry, callback) => addCard(entry, callback)"
      @refresh="(language) => refresh(language)"
      class="mt-8"
    />
  </div>
</template>