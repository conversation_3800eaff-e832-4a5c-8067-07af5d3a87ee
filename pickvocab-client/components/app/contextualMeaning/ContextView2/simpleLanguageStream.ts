import { Dictionary } from 'pickvocab-dictionary';
import { useEventBus } from '@vueuse/core';
import type { Ref } from 'vue';
import type { WordInContextEntry } from 'pickvocab-dictionary';
import type { LookupEvent } from './events';
import { useLLMStore } from '~/stores/llm';
import { nextLanguageRequestId, currentLanguageRequestId } from './requestIds';

interface LanguageLookupParams {
  language: string;
  dictionary: Dictionary;
  bus: ReturnType<typeof useEventBus<LookupEvent>>;
  wordEntryRef: Ref<WordInContextEntry | undefined>;
  word?: string;
  context?: string;
  offset?: number;
}

/**
 * Attempt a streaming lookup. Returns true if stream finished normally, false
 * if the caller should fall back to non-streaming.
 */
async function performLanguageStream(
  entry: WordInContextEntry,
  language: string,
  dictionary: Dictionary,
  bus: ReturnType<typeof useEventBus<LookupEvent>>,
  currentRequestId: string,
  currentLanguageRequestIdFn: () => string | undefined,
  wordEntryRef: Ref<WordInContextEntry | undefined>,
) {
  try {
    const generator = dictionary.getMeaningInContextShortForLanguageStream(entry, language);

    for await (const chunk of generator) {
      // Validate request ID to support soft-cancellation.
      if (currentLanguageRequestIdFn() !== currentRequestId) {
        return true; // Stop processing; not an error.
      }

      if (chunk.isComplete) {
        if (chunk.result) {
          applyFinalLanguageResult(wordEntryRef, chunk.result);
        }

        bus.emit({
          type: 'StreamCompleted',
          streamType: 'simple',
          language,
          requestId: currentRequestId,
        });
      } else {
        bus.emit({
          type: 'SimpleTextChunk',
          language,
          chunk: chunk.chunk,
          requestId: currentRequestId,
        });
      }
    }
    return true;
  } catch (err) {
    console.error('[simple-language-stream] Streaming failed', err);
    return false; // Signal caller to fallback
  }
}

async function performLanguageNonStreaming(
  entry: WordInContextEntry,
  language: string,
  dictionary: Dictionary,
  bus: ReturnType<typeof useEventBus<LookupEvent>>,
  currentRequestId: string,
  wordEntryRef: Ref<WordInContextEntry | undefined>,
) {
  try {
    const updatedEntry = await dictionary.getMeaningInContextShortForLanguage(entry, language);
    applyFinalLanguageResult(wordEntryRef, updatedEntry);

    bus.emit({
      type: 'StreamCompleted',
      streamType: 'simple',
      language,
      requestId: currentRequestId,
    });
  } catch (nonStreamError: any) {
    bus.emit({
      type: 'StreamError',
      streamType: 'simple',
      language,
      error: nonStreamError.message,
      requestId: currentRequestId,
    });
  }
}
// ------------------------------
// Helpers
// ------------------------------

/**
 * Ensure that `wordEntryRef.value` corresponds to the provided word/context.
 * If no entry exists (or it belongs to a different word), create a fresh one.
 * Returns a boolean indicating whether preparation succeeded.
 */
function ensureBaseEntry(
  wordEntryRef: Ref<WordInContextEntry | undefined>,
  language: string,
  props: { word?: string; context?: string; offset?: number },
): boolean {
  const { word, context, offset } = props;

  const needsNew =
    !wordEntryRef.value ||
    (word && wordEntryRef.value.word !== word) ||
    (context && wordEntryRef.value.context !== context) ||
    (offset !== undefined && wordEntryRef.value.offset !== offset);

  if (!needsNew) return true;

  if (!word || !context || offset === undefined) {
    console.warn('[simple-language-stream] Missing base parameters for language lookup');
    return false;
  }

  const llmStore = useLLMStore();
  wordEntryRef.value = {
    id: `temp-${Date.now()}`,
    word,
    context,
    offset,
    definitionShort: {
      explanation: '',
      languages: {
        [language]: { explanation: '' },
      },
    },
    definition: undefined,
    llm_model: llmStore.activeUserModel?.id || llmStore.pickvocabModel!.id,
  };

  return true;
}

/** Merge the streaming result back into the central entry while preserving ID */
function applyFinalLanguageResult(
  wordEntryRef: Ref<WordInContextEntry | undefined>,
  result: WordInContextEntry,
) {
  if (!wordEntryRef.value) return;
  const currentId = wordEntryRef.value.id;
  wordEntryRef.value = { ...wordEntryRef.value, ...result, id: currentId };
}

/**
 * Streams a simple definition in the given language for the current word.
 * Works concurrently with other language streams.
 */
export async function lookupSimpleLanguage(
  { language, dictionary, bus, wordEntryRef, word, context, offset }: LanguageLookupParams,
): Promise<void> {

  // Ensure we have a valid base entry for this lookup.
  const prepared = ensureBaseEntry(wordEntryRef, language, { word, context, offset });
  if (!prepared) return;

  const currentRequestId = nextLanguageRequestId(language);
  const entry = wordEntryRef.value!; // ensured by prepare

  // Emit stream start event so UI can prepare.
  bus.emit({
    type: 'StreamStarted',
    streamType: 'simple',
    language,
    requestDetails: { word: entry.word, context: entry.context, offset: entry.offset },
  });

  const streamed = await performLanguageStream(
    entry,
    language,
    dictionary,
    bus,
    currentRequestId,
    () => currentLanguageRequestId(language),
    wordEntryRef,
  );

  if (!streamed) {
    await performLanguageNonStreaming(
      entry,
      language,
      dictionary,
      bus,
      currentRequestId,
      wordEntryRef,
    );
  }
} 