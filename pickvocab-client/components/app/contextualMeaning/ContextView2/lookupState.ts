import { ref } from 'vue';
import { useEventBus } from '@vueuse/core';
import type { WordInContextEntry } from 'pickvocab-dictionary';
import { useLLMStore } from '~/stores/llm';
import type { LookupEvent } from './events';
import type { BaseWordInContextDefinition } from 'pickvocab-dictionary';
import { currentSimpleRequestId, currentLanguageRequestId, currentDetailedRequestId } from './requestIds';

/**
 * Provides reactive state for ContextView2 lookups and listens to the global
 * lookup event bus to apply updates coming from background streams.
 */
export function useLookupState() {
  const wordEntry = ref<WordInContextEntry>();
  const isLoading = ref(false);
  const isStreamingSimple = ref(false);
  const isStreamingDetailed = ref(false);
  const errorMessage = ref<string | null>(null);

  // Track which languages currently have an active simple stream.  
  // We wrap the Set in a ref so reactivity is preserved when the Set
  // reference changes. Direct mutation of a Set does **not** trigger
  // Vue reactivity, therefore we ALWAYS replace the Set with a new
  // instance whenever we add or remove a language.
  const activeSimpleLanguages = ref<Set<string>>(new Set());

  const llmStore = useLLMStore();
  const bus = useEventBus<LookupEvent>('lookup-events');

  bus.on((event) => {
    switch (event.type) {
      case 'StreamStarted': {
        if (event.streamType === 'simple') {
          isLoading.value = false;
          isStreamingSimple.value = true;
          errorMessage.value = null;

          const lang = event.language ?? 'English';
          if (!activeSimpleLanguages.value.has(lang)) {
            activeSimpleLanguages.value = new Set([
              ...activeSimpleLanguages.value,
              lang,
            ]);
          }

          // Replace central state only if this stream is for a *different*
          // (word, context, offset) triple. This prevents language streams for
          // the same word from wiping the entry and causing multiple creates.
          const needsReset =
            !wordEntry.value ||
            wordEntry.value.word !== event.requestDetails.word ||
            wordEntry.value.context !== event.requestDetails.context ||
            wordEntry.value.offset !== event.requestDetails.offset;

          if (needsReset) {
            wordEntry.value = {
              id: `temp-${Date.now()}`,
              word: event.requestDetails.word,
              context: event.requestDetails.context,
              offset: event.requestDetails.offset,
              definitionShort: { explanation: '' },
              definition: undefined,
              llm_model: llmStore.activeUserModel?.id || llmStore.pickvocabModel!.id,
            };
          }

          // Prepare field for the language that's being streamed.
          if (lang === 'English') {
            if (wordEntry.value!.definitionShort === undefined) {
              wordEntry.value!.definitionShort = { explanation: '' };
            }
            wordEntry.value!.definitionShort.explanation = '';
          } else {
            if (!wordEntry.value!.definitionShort) {
              wordEntry.value!.definitionShort = { explanation: '' };
            }
            if (!wordEntry.value!.definitionShort.languages) {
              wordEntry.value!.definitionShort.languages = {};
            }
            wordEntry.value!.definitionShort.languages![lang] = { explanation: '' };
          }
        }
        if (event.streamType === 'detailed') {
          isLoading.value = false;
          isStreamingDetailed.value = true;
          errorMessage.value = null;

          // Ensure we have a base word entry similar to the simple case.
          const needsReset =
            !wordEntry.value ||
            wordEntry.value.word !== event.requestDetails.word ||
            wordEntry.value.context !== event.requestDetails.context ||
            wordEntry.value.offset !== event.requestDetails.offset;

          if (needsReset) {
            wordEntry.value = {
              id: `temp-${Date.now()}`,
              word: event.requestDetails.word,
              context: event.requestDetails.context,
              offset: event.requestDetails.offset,
              definitionShort: { explanation: '' },
              definition: undefined,
              llm_model: llmStore.activeUserModel?.id || llmStore.pickvocabModel!.id,
            };
          }

          // Clear the definition to prepare for streaming (handles refresh).
          wordEntry.value!.definition = undefined;
        }
        break;
      }
      case 'SimpleTextChunk': {
        const lang = event.language ?? 'English';

        // Validate request ID to avoid stale updates.
        if (
          (lang === 'English' && event.requestId === currentSimpleRequestId()) ||
          (lang !== 'English' && event.requestId === currentLanguageRequestId(lang))
        ) {
          if (!wordEntry.value?.definitionShort) return;

          if (lang === 'English') {
            wordEntry.value.definitionShort.explanation += event.chunk;
          } else {
            if (!wordEntry.value.definitionShort.languages) {
              wordEntry.value.definitionShort.languages = {};
            }
            const langObj = wordEntry.value.definitionShort.languages[lang] || { explanation: '' };
            langObj.explanation += event.chunk;
            wordEntry.value.definitionShort.languages[lang] = langObj;
          }
        }
        break;
      }
      case 'DetailedPartialUpdate': {
        // Validate request ID to ensure we are updating the active detailed stream.
        if (event.requestId !== currentDetailedRequestId()) break;

        if (!wordEntry.value) break;

        const defPatch: Partial<BaseWordInContextDefinition> = event.definition;
        wordEntry.value.definition = {
          ...wordEntry.value.definition,
          ...defPatch,
        } as BaseWordInContextDefinition;
        break;
      }
      case 'StreamCompleted': {
        if (event.streamType === 'simple') {
          const lang = event.language ?? 'English';
          if (activeSimpleLanguages.value.has(lang)) {
            const next = new Set(activeSimpleLanguages.value);
            next.delete(lang);
            activeSimpleLanguages.value = next;
          }
          if (activeSimpleLanguages.value.size === 0) {
            isStreamingSimple.value = false;
          }
        } else if (event.streamType === 'detailed') {
          isStreamingDetailed.value = false;
        }
        break;
      }
      case 'StreamError': {
        if (event.streamType === 'simple') {
          const lang = event.language ?? 'English';
          if (activeSimpleLanguages.value.has(lang)) {
            const next = new Set(activeSimpleLanguages.value);
            next.delete(lang);
            activeSimpleLanguages.value = next;
          }
          if (activeSimpleLanguages.value.size === 0) {
            isStreamingSimple.value = false;
          }
        } else if (event.streamType === 'detailed') {
          isStreamingDetailed.value = false;
        }
        errorMessage.value = event.error;
        break;
      }
    }
  });

  return {
    wordEntry,
    isLoading,
    isStreamingSimple,
    isStreamingDetailed,
    errorMessage,
    activeSimpleLanguages,
  };
} 