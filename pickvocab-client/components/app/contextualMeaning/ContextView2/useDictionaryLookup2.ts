import { computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { Dictionary, type DictionarySource } from 'pickvocab-dictionary';
import { useEventBus } from '@vueuse/core';
import type { LookupEvent } from './events';
import { useLLMStore } from '~/stores/llm';
import { useLookupState } from './lookupState';
import { lookupSimpleEnglish } from './simpleEnglishStream';
import { lookupSimpleLanguage } from './simpleLanguageStream';
import { lookupDetailed } from './detailedStream';
import { useIdManager } from './idManager';
import { RemoteWordInContextApi } from '~/api/wordInContext';

export function useDictionaryLookup2(options?: { updateUrl?: boolean }) {
  // 1. Central reactive state & event-bus listeners.
  const {
    wordEntry,
    isLoading,
    isStreamingSimple,
    isStreamingDetailed,
    errorMessage,
    activeSimpleLanguages,
  } = useLookupState();

  // Phase 4: server synchronisation & URL management.
  // This will listen to stream-completion events and perform create / update
  // requests as well as replacing the route param once the real ID is known.
  useIdManager(wordEntry, { updateUrl: options?.updateUrl ?? false });

  const llmStore = useLLMStore();

  // 2. Reactive dictionary instance depending on active LLM.
  const dictionary = computed(() => {
    let sources: DictionarySource[] = [llmStore.pickvocabDictionarySource];
    if (llmStore.activeUserModel) {
      sources = [llmStore.createDictionarySource(llmStore.activeUserModel), ...sources];
    }
    return new Dictionary(sources);
  });

  // 3a. Public API — English simple lookup.
  async function lookupSimpleEnglishWrapper(word: string, context: string, offset: number) {
    const bus = useEventBus<LookupEvent>('lookup-events');
    await lookupSimpleEnglish({
      word,
      context,
      offset,
      dictionary: dictionary.value,
      bus,
      wordEntryRef: wordEntry,
    });
  }

  // 3b. Public API — Language simple lookup.
  async function lookupSimpleLanguageWrapper(
    language: string,
    opts?: { word: string; context: string; offset: number },
  ) {
    const bus = useEventBus<LookupEvent>('lookup-events');
    await lookupSimpleLanguage({
      language,
      dictionary: dictionary.value,
      bus,
      wordEntryRef: wordEntry,
      word: opts?.word,
      context: opts?.context,
      offset: opts?.offset,
    });
  }

  // 3c. Public API — Detailed English lookup.
  async function lookupDetailedWrapper(opts?: { word: string; context: string; offset: number }) {
    const bus = useEventBus<LookupEvent>('lookup-events');
    await lookupDetailed({
      dictionary: dictionary.value,
      bus,
      wordEntryRef: wordEntry,
      word: opts?.word,
      context: opts?.context,
      offset: opts?.offset,
    });
  }

  // 5. Expose reactive state & actions to consumers.
  return {
    wordEntry,
    isLoading,
    isStreamingSimple,
    isStreamingDetailed,
    errorMessage,
    activeSimpleLanguages,
    lookupSimpleEnglish: lookupSimpleEnglishWrapper,
    lookupSimpleLanguage: lookupSimpleLanguageWrapper,
    lookupDetailed: lookupDetailedWrapper,
  };
}