<script setup lang="ts">
import Skeleton from '~/components/ui/skeleton/Skeleton.vue';
import CardSkeleton from '~/components/app/cards/CardSkeleton.vue';

const props = withDefaults(defineProps<{
  cardCount?: number
}>(), {
  cardCount: 5
});
</script>

<template>
  <div class="py-10 px-4 lg:px-32 xl:px-44 2xl:px-64">
    <!-- Deck header skeleton -->
    <div class="flex items-center py-2 px-2 sm:px-0">
      <Skeleton class="h-8 w-48" />
      <Skeleton class="h-5 w-12 rounded-full ml-2" />
      <Skeleton class="h-8 w-20 ml-auto" />
    </div>
    
    <!-- Deck description skeleton -->
    <div class="mt-4">
      <Skeleton class="h-4 w-full mb-2" />
      <Skeleton class="h-4 w-3/4" />
    </div>
  </div>
  
  <!-- Cards skeleton -->
  <div class="px-4 lg:px-32 xl:px-44 2xl:px-64">
    <CardSkeleton :count="cardCount" />
  </div>
</template>