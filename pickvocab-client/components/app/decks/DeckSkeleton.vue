<script setup lang="ts">
import Skeleton from '~/components/ui/skeleton/Skeleton.vue';

const props = withDefaults(defineProps<{
  count?: number
}>(), {
  count: 5
});
</script>

<template>
  <div v-for="i in count" :key="i" class="mb-4">
    <div class="block p-6 bg-white border border-gray-200 rounded-lg shadow">
      <!-- Deck name and public badge -->
      <div class="mb-2 flex items-center gap-2">
        <Skeleton class="h-6 w-32" />
        <Skeleton class="h-5 w-12 rounded-full" />
      </div>
      
      <!-- Description -->
      <Skeleton class="h-4 w-full mb-2" />
      <Skeleton class="h-4 w-2/3" />
    </div>
  </div>
</template>