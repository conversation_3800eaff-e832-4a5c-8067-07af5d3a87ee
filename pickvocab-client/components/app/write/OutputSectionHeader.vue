<script setup lang="ts">
import { defineProps, defineEmits, ref } from 'vue';
// @ts-ignore
import IconMessageCircle from '@tabler/icons-vue/dist/esm/icons/IconMessageCircle.mjs';
// @ts-ignore
import IconRefresh from '@tabler/icons-vue/dist/esm/icons/IconRefresh.mjs';
// @ts-ignore
import IconCopy from '@tabler/icons-vue/dist/esm/icons/IconCopy.mjs';
// @ts-ignore
import IconChevronDown from '@tabler/icons-vue/dist/esm/icons/IconChevronDown.mjs';
// @ts-ignore
import IconWand from '@tabler/icons-vue/dist/esm/icons/IconWand.mjs';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import CustomInstructionDialog from './CustomInstructionDialog.vue';

const props = defineProps<{
  isRefreshing?: boolean; // Prop to control disabled state and spinner
  currentRevisionIndex?: number; // Current revision being viewed
  totalRevisions?: number; // Total number of revisions
}>();

const emits = defineEmits([
  'refreshRevision',
  'refreshWithCustomInstruction',
  'copyRevisedText',
]);

// Local state for custom instruction dialog
const showCustomInstructionDialog = ref(false);

// Handle standard refresh
function handleStandardRefresh() {
  emits('refreshRevision');
}

// Handle custom instruction dialog opening
function handleCustomInstructionClick() {
  showCustomInstructionDialog.value = true;
}

// Handle custom instruction dialog close
function handleCustomInstructionClose() {
  showCustomInstructionDialog.value = false;
}

// Handle custom instruction application
function handleCustomInstructionApply(instruction: string) {
  showCustomInstructionDialog.value = false;
  emits('refreshWithCustomInstruction', instruction);
}
</script>

<template>
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-xl font-semibold text-gray-800 flex items-center">
      <IconRefresh class="h-5 w-5 text-emerald-600 mr-2" />
      Revised Text
    </h2>
    <div class="flex space-x-2">
      <!-- Split refresh button group -->
      <div class="flex">
        <!-- Main refresh button -->
        <button 
          class="p-1.5 text-gray-600 hover:text-gray-900 bg-white border border-gray-200 shadow-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed rounded-l-md border-r-0 hover:bg-gray-100"
          @click="handleStandardRefresh"
          :disabled="props.isRefreshing"
          aria-label="Refresh Revision"
        >
          <IconRefresh class="h-5 w-5" />
        </button>
        
        <!-- Dropdown trigger for custom instructions -->
        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <button 
              class="px-1.5 py-1.5 text-gray-600 hover:text-gray-900 bg-white border border-gray-200 shadow-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed rounded-r-md hover:bg-gray-100"
              :disabled="props.isRefreshing"
              aria-label="Refresh Options"
            >
              <IconChevronDown class="h-4 w-4" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem @click="handleCustomInstructionClick">
              <IconWand class="h-4 w-4 mr-2" />
              Refresh with instruction
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      <!-- Copy button -->
      <button 
        class="p-1.5 text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 bg-white border border-gray-200 shadow-sm transition-colors"
        @click="$emit('copyRevisedText')"
        aria-label="Copy Revised Text"
      >
        <IconCopy class="h-5 w-5" />
      </button>
    </div>
    
    <!-- Custom Instruction Dialog -->
    <CustomInstructionDialog 
      :show="showCustomInstructionDialog"
      :is-refreshing="props.isRefreshing"
      :current-revision-index="props.currentRevisionIndex"
      :total-revisions="props.totalRevisions"
      @close="handleCustomInstructionClose"
      @apply="handleCustomInstructionApply"
    />
  </div>
</template>

<style scoped>
/* Component-specific styles if needed */
</style>