<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Dialog, DialogHeader, DialogTitle, DialogDescription, DialogContent, DialogFooter } from '~/components/ui/dialog';
// @ts-ignore
import IconRefresh from '@tabler/icons-vue/dist/esm/icons/IconRefresh.mjs';
// @ts-ignore
import IconClock from '@tabler/icons-vue/dist/esm/icons/IconClock.mjs';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs';

// Props
const props = defineProps<{
  show: boolean
  isRefreshing?: boolean // To show loading state during refresh
  currentRevisionIndex?: number // Current revision being viewed
  totalRevisions?: number // Total number of revisions
}>();

// Emits
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'apply', instruction: string): void
}>();

// Local state
const customInstruction = ref('');
const recentInstructions = ref<string[]>([]);
const isValid = ref(false);
const showRecentInstructions = ref(false);

// Constants
const RECENT_INSTRUCTIONS_KEY = 'pickvocab_recent_custom_instructions';
const MAX_RECENT_INSTRUCTIONS = 5;

// Predefined example instructions
const exampleInstructions = [
  'Make it shorter',
  'Make it more persuasive',
  'Use simpler language',
  'Add specific examples',
  'Don\'t use the word "..."',
  'Use the word "..."',
  'Remove passive voice',
  'Add transition sentences'
];

// Computed properties
const canApply = computed(() => {
  const trimmed = customInstruction.value.trim();
  return trimmed.length > 0;
});

// Validation watcher
watch(customInstruction, () => {
  isValid.value = canApply.value;
});

// Load recent instructions from localStorage
function loadRecentInstructions() {
  try {
    const stored = localStorage.getItem(RECENT_INSTRUCTIONS_KEY);
    if (stored) {
      recentInstructions.value = JSON.parse(stored);
    }
  } catch (error) {
    console.warn('Failed to load recent instructions from localStorage:', error);
    recentInstructions.value = [];
  }
}


// Save instruction to recent instructions
function saveRecentInstruction(instruction: string) {
  const trimmed = instruction.trim();
  if (!trimmed) return;

  // Remove if already exists to move to top
  const filtered = recentInstructions.value.filter(item => item !== trimmed);
  
  // Add to beginning
  recentInstructions.value = [trimmed, ...filtered].slice(0, MAX_RECENT_INSTRUCTIONS);

  // Save to localStorage
  try {
    localStorage.setItem(RECENT_INSTRUCTIONS_KEY, JSON.stringify(recentInstructions.value));
  } catch (error) {
    console.warn('Failed to save recent instructions to localStorage:', error);
  }
}

// Handle example instruction click
function selectInstruction(instruction: string) {
  customInstruction.value = instruction;
  showRecentInstructions.value = false;
}

// Handle apply button click
function handleApply() {
  if (!canApply.value) return;
  
  const instruction = customInstruction.value.trim();
  saveRecentInstruction(instruction);
  emit('apply', instruction);
}

// Handle close
function handleClose() {
  customInstruction.value = '';
  showRecentInstructions.value = false;
  emit('close');
}

// Handle dialog state change
function emitClose(value: boolean) {
  if (!value) handleClose();
}

// Handle Enter key in textarea
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
    event.preventDefault();
    if (canApply.value) {
      handleApply();
    }
  }
}

// Load recent instructions on mount
onMounted(() => {
  loadRecentInstructions();
});

// Watch dialog visibility to load recent instructions and clear input
watch(() => props.show, (newValue) => {
  if (newValue) {
    loadRecentInstructions();
    customInstruction.value = ''; // Clear previous instruction when dialog opens
  }
});
</script>

<template>
  <Dialog :open="show" @update:open="emitClose">
    <DialogContent class="sm:max-w-xl p-0 overflow-hidden">
      <!-- Header with gradient background -->
      <div class="bg-gradient-to-r from-emerald-50 to-green-50 p-6 border-b border-emerald-100">
        <DialogHeader class="space-y-2">
          <DialogTitle class="flex items-center gap-3 text-xl font-semibold text-gray-900">
            <div class="p-2 bg-emerald-100 rounded-lg">
              <IconRefresh class="h-5 w-5 text-emerald-600" />
            </div>
            Customize your revision
          </DialogTitle>
          <DialogDescription class="text-gray-600 text-sm">
            <span v-if="props.totalRevisions && props.totalRevisions > 1">
              Your instruction will be applied to <span class="font-medium text-gray-800">Revision {{ (props.currentRevisionIndex ?? 0) + 1 }}</span>
            </span>
            <span v-else>
              Your instruction will be applied to your current text
            </span>
          </DialogDescription>
        </DialogHeader>
      </div>

      <!-- Main content -->
      <div class="p-6 space-y-6">
        <!-- Primary: Custom instruction input -->
        <div class="space-y-3">
          <div class="relative">
            <textarea
              id="custom-instruction"
              v-model="customInstruction"
              placeholder="Enter your instruction here..."
              class="w-full min-h-[120px] text-base p-4 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-4 focus:ring-emerald-100 focus:border-emerald-400 resize-none bg-white transition-all duration-200 placeholder:text-gray-400"
              @keydown="handleKeydown"
            />
          </div>
        </div>

        <!-- Secondary: Quick suggestions -->
        <div class="space-y-4">
          <!-- Recent instructions (if any) -->
          <div v-if="recentInstructions.length > 0" class="space-y-3">
            <button
              type="button"
              @click="showRecentInstructions = !showRecentInstructions"
              class="group flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
            >
              <IconClock class="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
              Recent instructions
              <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                {{ recentInstructions.length }}
              </span>
            </button>
            
            <!-- Recent instructions dropdown -->
            <div 
              v-if="showRecentInstructions" 
              class="space-y-1 max-h-28 overflow-y-auto bg-gray-50 rounded-lg p-2 border border-gray-200"
            >
              <button
                v-for="(instruction, index) in recentInstructions"
                :key="index"
                type="button"
                @click="selectInstruction(instruction)"
                class="w-full text-left text-sm p-2.5 hover:bg-white rounded-md border border-transparent hover:border-gray-200 hover:shadow-sm transition-all text-gray-700 hover:text-gray-900"
              >
                "{{ instruction }}"
              </button>
            </div>
          </div>

          <!-- Quick examples -->
          <div class="space-y-3">
            <p class="text-sm font-medium text-gray-700">Or try these common instructions:</p>
            <div class="flex flex-wrap gap-2">
              <button
                v-for="example in exampleInstructions"
                :key="example"
                type="button"
                @click="selectInstruction(example)"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 bg-white border border-gray-200 rounded-full hover:bg-gray-50 hover:text-gray-900 hover:border-gray-300 transition-all duration-200 hover:shadow-sm"
              >
                {{ example }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
        <DialogFooter class="gap-3">
          <button
            type="button"
            @click="handleClose"
            class="w-full sm:w-auto px-4 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="button"
            @click="handleApply"
            :disabled="!canApply || isRefreshing"
            class="w-full sm:w-auto px-6 py-2.5 text-sm font-medium text-white bg-emerald-600 border border-transparent rounded-lg hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-2 shadow-sm hover:shadow"
          >
            <IconRefresh v-if="isRefreshing" class="h-4 w-4 animate-spin" />
            <span>{{ isRefreshing ? 'Applying...' : 'Apply' }}</span>
          </button>
        </DialogFooter>
      </div>
    </DialogContent>
  </Dialog>
</template>