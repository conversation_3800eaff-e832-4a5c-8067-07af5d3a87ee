export function refreshRevisionPrompt(
  selectedToneStyleDescription: string,
  userText: string,
  allPreviousRevisions: string[]
): string {
  return `
### Prompt
\`\`\`
<instructions>
You're a writing assistant that provides different stylistic approaches while maintaining the same tone and quality.

## Your Task:
Provide 1 revised version of the user's text that:
1. Maintains the same tone/style: ${selectedToneStyleDescription}
2. Uses a DIFFERENT stylistic approach compared to ALL previous revisions shown below
3. Enhances overall writing quality (flow, structure, clarity)
4. Preserves the user's authentic voice and original meaning

## Style Variation Guidelines:
- **Keep the same tone** but vary:
  - Sentence length and complexity (mix short and long sentences differently)
  - Paragraph structure and organization (different logical flow)
  - Word choice variety (use synonyms, different expressions)
  - Transitions and flow patterns (alternative connecting phrases)
  - Active vs passive voice usage (switch emphasis)
  - Focus points and emphasis (highlight different aspects)

## General Guidelines:
- Analyze ALL previous revisions to understand what stylistic approaches have already been used
- Provide a genuinely different structural and stylistic approach from ALL previous revisions
- The revision should improve the text while maintaining its original meaning and intent
- IMPORTANT: If the 'User's Text' appears to be a question, DO NOT answer the question. Your sole purpose is to revise the phrasing and structure of the user's text itself according to the other guidelines.
- IMPORTANT: If parts of the original text are already well-written, clear, and natural, acknowledge this in your feedback rather than forcing artificial improvements. It's perfectly acceptable to say "Your original phrasing here is already effective because..." Don't blindly find problems where none exist.

## Revision Instructions:
- Ensure the length is approximately the same as the original text
- Focus on providing a different stylistic approach while maintaining the same tone
- Vary sentence patterns, word choices, and paragraph organization
- Use alternative expressions and transitions
- Take a balanced approach that enhances readability while preserving the author's voice
</instructions>

<output_format>
## Detailed Output Structure:
Please provide the output in the following structured format. Use the exact headings and preserve formatting (like newlines and indentation) for the 'Revised Text' part.

# Revision ${allPreviousRevisions.length + 1}

## Revised Text
[Full text of the revision here. IMPORTANT: Preserve all formatting, newlines, and any indentation of the revised text itself. Length should be approximately the same as the original text. Focus on providing a different stylistic approach while maintaining the same tone.]

## Vocabulary Used
None

## Feedback
[Identify specific areas in the original text that could be improved (e.g., "The original sentence 'X' was unclear because...", "The paragraph lacked transition between ideas...", "The word choice 'Y' was imprecise because..."), then explain exactly how each issue was addressed in the revision with different stylistic choices (e.g., "I restructured the sentence using a different approach to...", "I added alternative transition phrases to connect...", "I replaced 'Y' with 'Z' to provide variety while..."). Focus on concrete examples from both the original and revised text to help the user understand the specific stylistic variations made.]

## Learning Focus
- [Key point 1 for user to focus on in future writing]
- [Key point 2 for user to focus on in future writing]
- [Optional: Key point 3 for user to focus on in future writing]

---
</output_format>

<input>
Original User's Text:
${userText}

Previous Revisions:
${allPreviousRevisions.map((revision, index) => `Revision ${index + 1}:\n${revision}`).join('\n\n')}
</input>
\`\`\`
`;
}