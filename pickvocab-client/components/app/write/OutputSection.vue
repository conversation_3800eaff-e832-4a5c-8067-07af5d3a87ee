<script setup lang="ts">
import { type PropType, computed } from 'vue';
import type { Card } from '~/utils/card'; // Import the base Card type
import OutputSectionHeader from './OutputSectionHeader.vue';
import RevisedTextViewer from './RevisedTextViewer.vue';
import UsedVocabularyList from './UsedVocabularyList.vue';
import LLMFeedbackDisplay from './LLMFeedbackDisplay.vue';
import CollapsibleSection from '~/components/ui/CollapsibleSection.vue';
import ShinyText from '~/components/ui/ShinyText.vue';
// @ts-ignore
import IconMessageCircle from '@tabler/icons-vue/dist/esm/icons/IconMessageCircle.mjs';
// @ts-ignore
import IconChevronLeft from '@tabler/icons-vue/dist/esm/icons/IconChevronLeft.mjs';
// @ts-ignore
import IconChevronRight from '@tabler/icons-vue/dist/esm/icons/IconChevronRight.mjs';
// @ts-ignore
import IconBook2 from '@tabler/icons-vue/dist/esm/icons/IconBook2.mjs';

const props = defineProps({
  isLoadingVocabularyCards: { type: Boolean, required: true }, // Add loading prop for vocabulary cards
  isStreamingRevision: { type: Boolean, required: false, default: false }, // Add streaming prop
  currentStreamingSection: { type: String as PropType<'revision' | 'feedback' | 'learning_focus' | null>, required: false, default: null }, // Add streaming section tracking
  revisedText: { type: String, required: false, default: '' }, // Made optional for backward compatibility
  originalTextForCopy: { type: String, required: false, default: '' }, // Made optional for backward compatibility
  // Removed feedbackMessage prop
  llmFeedbackText: { type: String, required: false, default: '' }, // Made optional for streaming
  learningFocus: { type: Array as PropType<string[]>, required: false, default: () => [] },
  usedVocabularyCards: {
    type: Array as PropType<Card[]>, // Use the base Card type
    required: true
  },
  currentRevisionIndex: { type: Number, required: true },
  totalRevisions: { type: Number, required: true },
  vocabularyWasUsed: { type: Boolean, required: true }, // Add new prop
  currentRevisionData: { type: Object, required: false }, // Add new prop for streaming data
});

const showLLMFeedback = defineModel<boolean>('showLLMFeedback', { default: false });
const showUsedVocabulary = defineModel<boolean>('showUsedVocabulary', { default: false });

// Computed property for dynamic streaming text based on current progress
const streamingText = computed(() => {
  if (props.isLoadingVocabularyCards) {
    return 'Finding relevant words from your notebooks';
  }
  
  switch (props.currentStreamingSection) {
    case 'feedback':
      return 'Crafting your personalized feedback';
    case 'learning_focus':
      return 'Creating your learning insights';
    case 'revision':
    default:
      return 'Enhancing your writing';
  }
});

const emits = defineEmits([
  'copyRevisedText',
  'refreshRevision',
  'refreshWithCustomInstruction',
  'toggleLLMFeedback',
  'toggleUsedVocabulary',
  'update:showLLMFeedback',
  'update:showUsedVocabulary',
  'navigateRevision',
]);


function navigate(direction: 'prev' | 'next') {
  emits('navigateRevision', direction);
}
</script>

<template>
  <!-- Output section content -->
  <div class="bg-white border border-gray-200 rounded-lg shadow-sm sm:p-5 px-2 py-5 space-y-4">
    <OutputSectionHeader
      @refresh-revision="emits('refreshRevision')"
      @refresh-with-custom-instruction="(instruction) => emits('refreshWithCustomInstruction', instruction)"
      @copy-revised-text="emits('copyRevisedText')"
      :is-refreshing="props.isStreamingRevision || props.isLoadingVocabularyCards"
      :current-revision-index="props.currentRevisionIndex"
      :total-revisions="props.totalRevisions"
    />

    <!-- Single unified streaming indicator -->
    <div 
      class="overflow-hidden transition-all duration-300 ease-out"
      :class="{ 'max-h-0 opacity-0': !props.isStreamingRevision, 'max-h-12 opacity-100': props.isStreamingRevision }"
    >
      <div class="flex items-center justify-center py-3">
        <div class="flex items-center space-x-2 text-gray-500">
          <!-- Show dynamic text based on current progress -->
          <ShinyText 
            :text="streamingText" 
            class="text-sm" 
            :speed="2" 
          />
          <div class="flex items-center space-x-1">
            <span class="animate-dot-1">.</span>
            <span class="animate-dot-2">.</span>
            <span class="animate-dot-3">.</span>
          </div>
        </div>
      </div>
    </div>

    <RevisedTextViewer
      :revised-text="props.revisedText"
      :original-text-for-copy="props.originalTextForCopy"
      :revision="props.currentRevisionData"
    />

    <!-- Revision Navigation Controls -->
    <div v-if="props.totalRevisions > 1" class="flex items-center justify-center space-x-4 mt-2 mb-2">
      <button
        @click="navigate('prev')"
        :disabled="props.currentRevisionIndex === 0"
        class="p-2 rounded-md text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="Previous revision"
      >
        <IconChevronLeft class="w-5 h-5" />
      </button>
      <span class="text-sm font-medium text-gray-700">
        Revision {{ props.currentRevisionIndex + 1 }} of {{ props.totalRevisions }}
      </span>
      <button
        @click="navigate('next')"
        :disabled="props.currentRevisionIndex >= props.totalRevisions - 1"
        class="p-2 rounded-md text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="Next revision"
      >
        <IconChevronRight class="w-5 h-5" />
      </button>
    </div>


    <!-- Feedback section appears when LLM reaches feedback or we have feedback data -->
    <CollapsibleSection
      v-if="props.llmFeedbackText || props.currentRevisionData?.feedback !== undefined"
      v-model="showLLMFeedback"
      :title="'Feedback'"
      :icon-component="IconMessageCircle"
    >
      <LLMFeedbackDisplay
        :llm-feedback-text="props.currentRevisionData?.feedback || props.llmFeedbackText"
        :learning-focus="props.currentRevisionData?.learning_focus || props.learningFocus"
      />
    </CollapsibleSection>

    <!-- Vocabulary section appears when LLM reaches vocabulary (vocabulary flow only) -->
    <CollapsibleSection
      v-if="props.vocabularyWasUsed && (props.usedVocabularyCards.length > 0 || props.currentRevisionData?.user_vocabularies_used !== undefined)"
      v-model="showUsedVocabulary"
      :title="'Used Vocabulary'"
      :icon-component="IconBook2"
      :item-count="props.usedVocabularyCards.length"
    >
      <UsedVocabularyList
        :used-vocabulary-cards="props.usedVocabularyCards"
        :is-loading-vocabulary-cards="props.isLoadingVocabularyCards"
      />
    </CollapsibleSection>
  </div>
</template>

<style scoped>
.animate-dot-1 {
  animation: dot-pulse 1.4s infinite linear;
  animation-delay: 0s;
}

.animate-dot-2 {
  animation: dot-pulse 1.4s infinite linear;
  animation-delay: 0.2s;
}

.animate-dot-3 {
  animation: dot-pulse 1.4s infinite linear;
  animation-delay: 0.4s;
}

@keyframes dot-pulse {
  0%, 80%, 100% {
    opacity: 0.3;
  }
  40% {
    opacity: 1;
  }
}
</style>