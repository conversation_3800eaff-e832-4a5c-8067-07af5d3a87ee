<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Dialog, DialogContent, DialogClose } from '@/components/ui/dialog';
import ExplanationView from '~/components/app/contextualMeaning/ExplanationView.vue';
import DictionaryWordViewInfoAlert from '~/components/app/dictionary/DictionaryWordViewInfoAlert.vue';
import type { WordInContextEntry } from 'pickvocab-dictionary';
import type { CardId, BaseContextCard, ContextCard } from '~/utils/card';
import { useCreateContextCard } from '~/composables/useCardMutations';
import { useDictionaryLookup2 } from '~/components/app/contextualMeaning/ContextView2/useDictionaryLookup2';
import { useLLMStore } from '~/stores/llm';
import { useRouter } from 'vue-router';

// Stores / router
const llmStore = useLLMStore();
const router = useRouter();

// -----------------------
// Props / Emits
// -----------------------
const props = defineProps<{
  word: string;
  context: string;
  offset: number;
  open: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:open', value: boolean): void;
  (e: 'addCard', wordEntry: WordInContextEntry, callback?: () => void): void;
}>();

// -----------------------
// Dictionary composable (new arch)
// -----------------------
const {
  wordEntry,
  isLoading,
  isStreamingSimple,
  isStreamingDetailed,
  errorMessage,
  activeSimpleLanguages,
  lookupSimpleEnglish,
  lookupSimpleLanguage,
  lookupDetailed,
} = useDictionaryLookup2();

// -----------------------
// Local state
// -----------------------
const isDetailed = ref(false);
const selectedSimpleViewLanguage = ref<string>('English');

const isCreatingCardState = ref(false);
const isSaved = ref(false);
const savedCardId = ref<CardId | null>(null);
const showDefinitionView = ref(true);

// TanStack Query mutation for proper cache management (same pattern as ContextView)
const { mutate: createContextCardMutation, isPending: isCreatingCard } = useCreateContextCard();
// -----------------------
// Derived state
// -----------------------
const isStreamingSimpleCurrentLang = computed(() => {
  if (!isStreamingSimple.value) return false;
  const lang = selectedSimpleViewLanguage.value || 'English';
  return activeSimpleLanguages.value.has(lang);
});

const cardUrl = computed(() => {
  return savedCardId.value ? `/app/cards/${savedCardId.value}` : '';
});

const llmModel = computed(() => {
  const id = wordEntry.value?.llm_model;
  return typeof id === 'number' ? llmStore.getModelById(id) : undefined;
});

const showInfoMessage = computed(() => {
  return llmStore.shouldShowAPIKeyAlert();
});

// -----------------------
// Helpers
// -----------------------
function ensureSimpleDefinition(lang: string) {
  if (!wordEntry.value) return;

  if (lang === 'English') {
    if (!wordEntry.value.definitionShort?.explanation) {
      lookupSimpleEnglish(wordEntry.value.word, wordEntry.value.context, wordEntry.value.offset);
    }
    return;
  }

  const hasExplanation =
    wordEntry.value.definitionShort?.languages &&
    wordEntry.value.definitionShort.languages[lang] &&
    wordEntry.value.definitionShort.languages[lang].explanation;

  if (!hasExplanation) {
    void lookupSimpleLanguage(lang);
  }
}

async function handleLookup() {
  if (!props.word || !props.context || props.offset === undefined) return;

  if (isDetailed.value) {
    lookupDetailed({ word: props.word, context: props.context, offset: props.offset });
    return;
  }

  if (selectedSimpleViewLanguage.value === 'English') {
    lookupSimpleEnglish(props.word, props.context, props.offset);
  } else {
    lookupSimpleLanguage(selectedSimpleViewLanguage.value, {
      word: props.word,
      context: props.context,
      offset: props.offset,
    });
  }
}

function refresh(language?: string) {
  if (!wordEntry.value) return;

  if (isDetailed.value) {
    lookupDetailed();
    return;
  }

  const lang = language ?? selectedSimpleViewLanguage.value ?? 'English';
  if (lang === 'English') {
    lookupSimpleEnglish(wordEntry.value.word, wordEntry.value.context, wordEntry.value.offset);
  } else {
    lookupSimpleLanguage(lang);
  }
}

async function addCard(entry: WordInContextEntry, callback?: () => void) {
  isCreatingCardState.value = true;
  try {
    const baseCard: BaseContextCard = {
      wordInContext: entry,
    };

    const card = await new Promise<ContextCard>((resolve, reject) => {
      createContextCardMutation(baseCard, {
        onSuccess: (createdCard) => resolve(createdCard),
        onError: (error) => reject(error),
      });
    });

    savedCardId.value = card.id;
    isSaved.value = true;
    showDefinitionView.value = false;
  } catch (error) {
    console.error('Error creating card:', error);
    errorMessage.value = `Failed to create card: ${error}` as any;
  } finally {
    isCreatingCardState.value = false;
    callback && callback();
  }
}

function backToDefinitionView() {
  showDefinitionView.value = true;
}

async function setupApiKey() {
  emit('update:open', false);
  await router.push('/app/reader?showApiModal=true');
}

async function hideApiKeyAlert() {
  llmStore.lastShowAPIKeyAlert = Date.now();
}

// -----------------------
// Watchers
// -----------------------
watch(
  () => props.open,
  async (val) => {
    if (val) {
      showDefinitionView.value = true;
      await handleLookup();
    } else {
      // Reset state when modal closes
      wordEntry.value = undefined;
    }
  },
);

// Watch for detailed/simple toggle
watch(isDetailed, (newVal) => {
  if (!wordEntry.value) return;
  if (newVal) {
    if (!wordEntry.value.definition) {
      lookupDetailed();
    }
  } else {
    ensureSimpleDefinition(selectedSimpleViewLanguage.value ?? 'English');
  }
});

// Watch for language changes
watch(selectedSimpleViewLanguage, (newLang) => {
  if (!newLang) return;
  ensureSimpleDefinition(newLang);
});

// When wordEntry changes (e.g., after lookup), ensure proper definition is loaded
watch(
  wordEntry,
  (entry) => {
    if (!entry) return;
    if (isDetailed.value) {
      if (!entry.definition) lookupDetailed();
    } else {
      ensureSimpleDefinition(selectedSimpleViewLanguage.value ?? 'English');
    }
  },
  { immediate: true },
);
</script>

<template>
  <Dialog v-model:open="props.open" @update:open="emit('update:open', $event)">
    <DialogContent class="max-w-2xl w-full max-h-[90dvh] overflow-y-auto">
      <DialogClose />

      <!-- Info Alert -->
      <DictionaryWordViewInfoAlert
        v-if="showInfoMessage"
        class="mb-4"
        @setup="setupApiKey()"
        @dismiss="hideApiKeyAlert()"
      />

      <!-- Error message -->
      <div v-if="errorMessage" class="my-4 text-red-600">{{ errorMessage }}</div>

      <!-- Definition view -->
      <div v-if="showDefinitionView">
        <ExplanationView
          :word="props.word"
          :word-entry="wordEntry"
          :llm-model="llmModel"
          :is-loading="isLoading"
          :is-streaming-simple="isStreamingSimpleCurrentLang"
          :is-streaming-detailed="isStreamingDetailed"
          v-model:is-detailed="isDetailed"
          v-model:selected-simple-view-language="selectedSimpleViewLanguage"
          @add-card="addCard"
          @refresh="refresh"
          class="border-none !py-0 !px-4"
        />
      </div>

      <!-- Success view (after card creation) -->
      <div v-else class="p-8 text-center">
        <div class="flex flex-col items-center justify-center">
          <p class="text-lg font-semibold text-gray-800 mb-4">
            Card saved successfully!
          </p>
          <a :href="cardUrl" target="_blank" class="text-blue-600 hover:text-blue-800 mb-6">
            View card
          </a>
          <button
            type="button"
            @click="backToDefinitionView"
            class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 text-center"
          >
            Back to Definition
          </button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<style scoped></style> 