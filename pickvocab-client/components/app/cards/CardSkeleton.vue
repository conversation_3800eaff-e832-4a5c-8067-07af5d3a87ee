<script setup lang="ts">
import Skeleton from '~/components/ui/skeleton/Skeleton.vue';

const props = withDefaults(defineProps<{
  count?: number
}>(), {
  count: 5
});
</script>

<template>
  <div v-for="i in count" :key="i" class="mb-4">
    <div class="block p-6 bg-white border border-gray-200 rounded-lg shadow">
      <!-- Header section with word and mastery badge -->
      <div class="mb-2 flex items-center justify-between">
        <div class="flex items-center flex-wrap gap-2">
          <!-- Word skeleton -->
          <Skeleton class="h-6 w-24" />
          <!-- Part of speech tag skeleton -->
          <Skeleton class="h-5 w-16 rounded-xl" />
        </div>
        <!-- Mastery level badge skeleton -->
        <Skeleton class="h-6 w-20 rounded-full" />
      </div>
      
      <!-- Definition skeleton -->
      <Skeleton class="h-4 w-full mb-2" />
      <Skeleton class="h-4 w-3/4 mb-2" />
      
      <!-- Example/context skeleton -->
      <Skeleton class="h-3 w-5/6 mt-2" />
    </div>
  </div>
</template>