<script setup lang="ts">
import InputSection from '~/components/app/write/InputSection.vue';
import ManageCustomTonesDialog from '~/components/app/write/ManageCustomTonesDialog.vue';
import DictionaryWordViewErrorAlert from '~/components/app/dictionary/DictionaryWordViewErrorAlert.vue'; // Added for error display
import OutputSection from '~/components/app/write/OutputSection.vue';
import { useWriteRevisionHandler } from '~/components/app/write/useWriteRevisionHandler'; // Import the composable
import type { Card } from '~/utils/card'; // Import the base Card type
// Imports moved to composable: RemoteGenericCardsApi, useMyVocabularyPrompt, CardType, Card
import { ref, watch, onMounted, computed } from 'vue'; // Import watch and onMounted
import { RemoteGenericCardsApi } from '~/api/genericCard'; // Import the API for checking vocabulary cards
import { useAuthStore } from '~/stores/auth';

// State for the component
import { useToneStore } from '~/stores/toneStore';
const toneStore = useToneStore();
const authStore = useAuthStore();

const userText = ref('');
const useVocabulary = ref(false);
const grammarCheck = ref(false);
const showAddToneDialog = ref(false);
const showLLMFeedback = ref(false);
const showUsedVocabulary = ref(false);
// State for vocabulary cards check
const hasVocabularyCards = ref<boolean | null>(null); // Initial value null means "not loaded yet"
const isCheckingVocabularyCards = ref(false);

// Function to copy the revised text to clipboard
const copyRevisedText = () => {
  // Get the original unhighlighted text content from the composable's state
  const textToCopy = originalRevisedTextResult.value;

  // Copy to clipboard
  navigator.clipboard.writeText(textToCopy)
    .then(() => {
      console.log('Original revised text copied to clipboard.');
    })
    .catch(err => {
      console.error('Failed to copy original revised text: ', err);
    });
};

// Define the type for tone names


// Removed old sendFeedback function

// Use the composable and destructure all returned state and functions
const {
  isRevising,
  isGeneratingRevision,
  isStreamingRevision,
  initiateRevision,
  refreshRevision,
  refreshRevisionWithCustomInstruction,
  restoreFromHistory,
  revisedTextResult,
  originalRevisedTextResult,
  llmFeedbackResult,
  learningFocusResult,
  usedVocabularyCardsResult,
  currentRevisionData,
  currentRevisionIndex,
  allRevisionsResult,
  nextRevision,
  previousRevision,
  revisionError,
  vocabularyWasUsedForLastRevision,
  isLoadingVocabularyCards,
  currentStreamingSection,
} = useWriteRevisionHandler(
  userText,
  useVocabulary,
  grammarCheck
);

// Function to check if user has vocabulary cards
async function checkVocabularyCards() {
  if (isCheckingVocabularyCards.value) return;
  
  try {
    isCheckingVocabularyCards.value = true;
    const genericCardApi = new RemoteGenericCardsApi();
    
    // Get current user ID from auth store
    const currentUser = authStore.currentUser;
    
    // Create params object with owner filter if user ID is available
    const params: { owner?: number } = {};
    if (currentUser && currentUser.id) {
      params.owner = currentUser.id;
    }
    
    const cardsListResult = await genericCardApi.list(params);
    
    // Update the ref based on totalPages - if 0, user has no cards
    hasVocabularyCards.value = cardsListResult.totalPages > 0;
  } catch (error) {
    console.error('Failed to check vocabulary cards:', error);
    // Default to true on error to avoid showing the warning incorrectly
    hasVocabularyCards.value = true;
  } finally {
    isCheckingVocabularyCards.value = false;
  }
}

// --- LocalStorage Persistence ---
const STORAGE_KEY_USE_VOCAB = 'pickvocab.write.useVocabulary';
const STORAGE_KEY_GRAMMAR_CHECK = 'pickvocab.write.grammarCheck';
const STORAGE_KEY_SELECTED_TONE = 'pickvocab.write.selectedTone';

onMounted(async () => {
  toneStore.loadPredefinedTones();
  toneStore.fetchCustomTones();

  // Load settings from localStorage
  const savedUseVocab = localStorage.getItem(STORAGE_KEY_USE_VOCAB);
  if (savedUseVocab !== null) {
    useVocabulary.value = savedUseVocab === 'true';
  }

  const savedGrammarCheck = localStorage.getItem(STORAGE_KEY_GRAMMAR_CHECK);
  if (savedGrammarCheck !== null) {
    grammarCheck.value = savedGrammarCheck === 'true';
  }

  const savedTone = localStorage.getItem(STORAGE_KEY_SELECTED_TONE);
  if (savedTone !== null) {
    try {
      const parsed = JSON.parse(savedTone);
      toneStore.selectTone(parsed);
    } catch {
      toneStore.selectTone(savedTone ? { type: 'predefined', identifier: savedTone } : null);
    }
  }
  
  // Check if user has vocabulary cards
  await checkVocabularyCards();
});

watch(useVocabulary, (newValue) => {
  localStorage.setItem(STORAGE_KEY_USE_VOCAB, String(newValue));
});

watch(grammarCheck, (newValue) => {
  localStorage.setItem(STORAGE_KEY_GRAMMAR_CHECK, String(newValue));
});

watch(
  () => toneStore.selectedTone,
  (newValue) => {
    localStorage.setItem(STORAGE_KEY_SELECTED_TONE, newValue ? JSON.stringify(newValue) : '');
  },
  { deep: true }
);
// --- End LocalStorage Persistence ---

// Calculate total revisions for display
const totalRevisions = computed(() => allRevisionsResult.value.length);

// Combined loading state for the UI
const isLoading = computed(() => isRevising.value || isGeneratingRevision.value || isStreamingRevision.value);

// Function to handle clicking on a vocabulary card
const viewVocabularyDetails = (_card: Card) => { // Use the base Card type
  // In a real implementation, this would navigate to the card details or show a modal
  // For demonstration, you could add a dialog here or navigate to the card detail page
};

// Function to handle navigation events from OutputSection
const handleNavigateRevision = (direction: 'prev' | 'next') => {
  if (direction === 'prev') {
    previousRevision();
  } else if (direction === 'next') {
    nextRevision();
  }
};


// Function to handle restoring from history
function handleRestoreHistory(historyEntry: any) {
  restoreFromHistory(historyEntry);
}

useSeoMeta({
  title: 'Writing Assistant | Pickvocab'
});

definePageMeta({
  layout: 'app'
})
</script>

<template>
  <NuxtLayout name="app">
    <div class="w-full h-full box-border bg-gray-50">
      <div class="sm:ml-64 mt-14 bg-gray-50">
        <div class="py-10 px-4 lg:px-24 xl:px-32 2xl:px-48">
          <div class="mb-8">
            <h1 class="text-2xl font-bold text-gray-800">Writing Assistant</h1>
            <p class="text-gray-600 mt-1">Enhance your writing with AI-powered assistant</p>
          </div>

          <div class="space-y-8">
            <!-- Input Section -->
            <InputSection
              v-model:userText="userText"
              v-model:selectedTone="toneStore.selectedTone"
              v-model:useVocabulary="useVocabulary"
              v-model:grammarCheck="grammarCheck"
              @revise="initiateRevision"
              :is-revising="isLoading"
              :has-vocabulary-cards="hasVocabularyCards ?? undefined"
              @openManageTones="showAddToneDialog = true"
              @restoreHistory="handleRestoreHistory"
            />
            <!-- Error Display -->
            <DictionaryWordViewErrorAlert
              v-if="revisionError"
              class="mt-4"
              :message="revisionError"
              :is-active-user-model="true"
            />

            <!-- Output Section -->
            <OutputSection
              v-if="totalRevisions > 0 || isLoading"
              :is-loading-vocabulary-cards="isLoadingVocabularyCards"
              :is-streaming-revision="isStreamingRevision"
              :current-streaming-section="currentStreamingSection"
              :revised-text="revisedTextResult"
              :original-text-for-copy="originalRevisedTextResult"
              :current-revision-data="currentRevisionData"
              v-model:showLLMFeedback="showLLMFeedback"
              v-model:showUsedVocabulary="showUsedVocabulary"
              :llm-feedback-text="llmFeedbackResult"
              :learning-focus="learningFocusResult"
              :used-vocabulary-cards="usedVocabularyCardsResult"
              :current-revision-index="currentRevisionIndex"
              :total-revisions="totalRevisions"
              :vocabulary-was-used="vocabularyWasUsedForLastRevision"
              @copyRevisedText="copyRevisedText"
              @refreshRevision="refreshRevision"
              @refreshWithCustomInstruction="refreshRevisionWithCustomInstruction"
              @viewVocabularyDetails="viewVocabularyDetails"
              @navigateRevision="handleNavigateRevision"
            />
          </div>
        </div>
      </div>
    </div>

    <ManageCustomTonesDialog
      :show="showAddToneDialog"
      @close="showAddToneDialog = false"
    />
  </NuxtLayout>
</template>

<style scoped>
/* CSS related to hover and delete button removed */
</style>