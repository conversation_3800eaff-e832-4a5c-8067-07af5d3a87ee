import type { ChatMessage, ChatSource, ChatResponse } from "pickvocab-dictionary";

export class NvidiaNimChatClient implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName: string;
  maxTokens = 8192;
  isThinkingModel = false;
  messages: unknown[] = [];

  constructor(config: { modelName: string, modelId: number, maxToken?: number, apiKey: string, isThinkingModel?: boolean }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config?.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.isThinkingModel = config.isThinkingModel ?? false;
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages;
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    const response = await $fetch('/api/nvidiaNimChat', {
      method: 'POST',
      body: {
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        isThinkingModel: this.isThinkingModel,
        endpoint: 'sendMessage',
        args: [this.messages, message]
      }
    });
    this.messages.push({ role: 'user', content: message });
    this.messages.push({
      role: 'assistant',
      content: response.message
    });
    return response as ChatResponse;
  }

  async *sendMessageStream(message: string): AsyncGenerator<{ chunk: string; isComplete: boolean; result?: ChatResponse }> {
    const response = await fetch('/api/nvidiaNimChat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        isThinkingModel: this.isThinkingModel,
        endpoint: 'sendMessageStream',
        args: [this.messages, message]
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No reader available');
    }

    const decoder = new TextDecoder();
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n').filter(line => line.trim() !== '');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            try {
              const parsed = JSON.parse(data);
              
              if (parsed.isComplete) {
                // Update message history
                this.messages.push({ role: 'user', content: message });
                this.messages.push({
                  role: 'assistant',
                  content: parsed.result.message
                });
              }
              
              yield parsed;
              
              if (parsed.isComplete) {
                return;
              }
            } catch (e) {
              console.error('Failed to parse streaming data:', data);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }
}