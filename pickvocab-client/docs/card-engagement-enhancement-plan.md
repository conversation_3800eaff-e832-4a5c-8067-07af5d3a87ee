# Card Engagement Enhancement Plan

## Overview

This document outlines the plan to enhance card views in Pickvocab by adding **progress indicators** and **deck membership** information to make cards more engaging and informative.

## Current State Analysis

### Available Data
- **Progress Score**: `progress_score` field (0-5 integer scale) available in card objects
- **Mastery Levels**: Defined in `reviewSummaryTypes.ts`:
  - 0: "New"
  - 1-2: "Learning" 
  - 3: "Familiar"
  - 4: "Well Known"
  - 5: "Mastered"
- **Deck Membership**: Backend relationship exists but not exposed via API

### Current Display Issues
- Card entries only show basic info (word, definition, example)
- No visual progress indicators
- No deck membership information
- Missing engagement metrics

## Enhancement Plan

### Phase 1: Progress Display (Immediate Implementation)

**Objective**: Add mastery level indicators to existing card components using available `progress_score` data.

#### Display Strategy by View Type

**Card List Views (AllCardView):**
- **ONLY show mastery level badges** - keep clean and scannable
- No deck information to avoid visual clutter
- Focus on quick progress assessment across many cards

**Individual Card View (CardView):**
- **Show mastery level prominently** in header
- **Reserve space for future deck membership display** (Phase 2)
- More comprehensive information appropriate for detailed view

#### Components to Update

1. **`DefinitionCardEntry.vue`** (Card List)
   - Add mastery level badge next to card title
   - Color-coded based on mastery level
   - **NO deck information**

2. **`ContextCardEntry.vue`** (Card List)
   - Add mastery level badge next to card title
   - Consistent with DefinitionCardEntry styling
   - **NO deck information**

3. **`CardView.vue`** (Individual card view)
   - Prominent mastery level display in header
   - Enhanced progress information
   - **Future location for deck membership** (Phase 2)

#### Visual Design

**Mastery Level Badge System:**
- **New (0)**: Gray badge with "New" text
- **Learning (1-2)**: Yellow badge with "Learning" text
- **Familiar (3)**: Blue badge with "Familiar" text
- **Well Known (4)**: Green badge with "Well Known" text
- **Mastered (5)**: Gold badge with "Mastered" text

**Card List Layout (Clean & Scannable):**
```
[Card Title] [Mastery Badge]
Definition text...
Example text...
```

**Individual Card Layout (Comprehensive):**
```
[Card Title] — [Mastery Level Badge]
[Future: Deck membership area]
[Other card details...]
```

#### Implementation Details

1. **Create utility function** in `utils/card.ts`:
   ```typescript
   export function getMasteryLevel(score: number): string {
     // Use existing MASTERY_LEVELS from reviewSummaryTypes.ts
   }
   
   export function getMasteryLevelColor(score: number): string {
     // Return appropriate Tailwind color classes
   }
   ```

2. **Update card entry components**:
   - Import mastery level utilities
   - Add computed property for mastery level
   - Add badge component to template

3. **Update individual card view**:
   - Add prominent mastery level display
   - Consider progress visualization

### Phase 2: Deck Membership (Requires Backend Support)

**Objective**: Display which decks contain each card **ONLY in individual card view (CardView)**.

**Important**: Deck membership will **NOT** be shown in card list views to maintain clean, scannable interface.

#### Backend Requirements

**New API Endpoint Needed:**
```python
# In GenericCardViewSet
@action(detail=True, methods=["get"])
def get_decks(self, request, pk=None):
    """Get all decks that contain this card"""
    card = self.get_object()
    decks = card.decks.filter(owner=request.user)
    serializer = DeckSerializer(decks, many=True)
    return Response(serializer.data)
```

**Endpoint**: `GET /api/generic_cards/{card_id}/decks/`

#### Frontend Implementation

1. **Add API method** in `api/genericCard.ts`:
   ```typescript
   async getCardDecks(cardId: CardId): Promise<Deck[]> {
     // Implementation
   }
   ```

2. **Create composable** `useCardDecksQuery.ts`:
   ```typescript
   export function useCardDecksQuery(cardId: Ref<CardId>) {
     // TanStack Query implementation
   }
   ```

3. **Update individual card view only**:
   - Add deck membership display to `CardView.vue`
   - Show deck chips/badges
   - Handle multiple decks gracefully
   - **DO NOT add to card list components**

#### Visual Design for Deck Membership

**Deck Badges:**
- Small chips showing deck names
- Truncated list for multiple decks ("Deck 1, +2 more")
- Color-coded or styled consistently

**Individual Card Layout with Decks (CardView only):**
```
[Card Title] — [Mastery Level Badge]
[Deck Badge 1] [Deck Badge 2] [+2 more]
[Other card details...]
```

**Card List Layout (NO deck information):**
```
[Card Title] [Mastery Badge]
Definition text...
Example text...
```

## Implementation Priority

### Immediate (Phase 1)
1. ✅ Create mastery level utility functions
2. ✅ Update `DefinitionCardEntry.vue` with mastery badges
3. ✅ Update `ContextCardEntry.vue` with mastery badges
4. ✅ Update `CardView.vue` with enhanced progress display
5. ✅ Test and refine visual design

### Future (Phase 2 - After Backend Support)
1. ❌ Add backend API endpoint for card decks
2. ❌ Add frontend API method
3. ❌ Create composable for fetching card decks
4. ❌ Update card components with deck membership
5. ❌ Test and refine deck display

## Technical Implementation Notes

### Mastery Level Mapping
- Reuse existing `MASTERY_LEVELS` from `components/app/reviews/reviewSummaryTypes.ts`
- Handle undefined/null `progress_score` values (default to 0 or 3)

### Color Scheme
- Use Tailwind CSS classes for consistency
- Ensure accessibility with proper contrast
- Consider dark mode compatibility

### Performance Considerations
- Phase 1 has no performance impact (uses existing data)
- Phase 2 will require additional API calls - use TanStack Query for caching

### Current Limitations
- **Deck membership**: Requires backend API development
- **Progress history**: Not available in current data model
- **Review statistics**: Would require additional backend support

## File Structure

### Files to Modify (Phase 1) - Mastery Level Only
- `utils/card.ts` - Add mastery level utilities
- `components/app/cards/DefinitionCardEntry.vue` - Add mastery badges (NO decks)
- `components/app/cards/ContextCardEntry.vue` - Add mastery badges (NO decks)
- `components/app/cards/CardView.vue` - Enhanced progress display (NO decks yet)

### Files to Modify (Phase 2) - Deck Membership
- `components/app/cards/CardView.vue` - Add deck membership display
- **NOTE**: Card list components (`DefinitionCardEntry.vue`, `ContextCardEntry.vue`) will NOT be modified

### Files to Create (Phase 2)
- `composables/useCardDecksQuery.ts` - Deck membership query
- Backend endpoint in Django

## Success Metrics

### Phase 1 Success Criteria
- All card list items show mastery level badges
- Individual card views display progress prominently
- Visual design is consistent and accessible
- No performance degradation

### Phase 2 Success Criteria
- Cards display deck membership information
- Deck badges are informative and not overwhelming
- API performance is acceptable
- User experience is enhanced

## Future Enhancements

Beyond this plan, consider:
- **Study streaks**: Track consecutive review sessions
- **Difficulty indicators**: Show how challenging words are
- **Recent activity**: Show last review date
- **Quick actions**: Study/practice buttons in card list
- **Progress trends**: Historical progress visualization

## Dependencies

### Phase 1 Dependencies
- Existing `progress_score` data in cards
- Current mastery level mapping system
- Tailwind CSS for styling

### Phase 2 Dependencies
- Backend API development
- Django model relationship (already exists)
- TanStack Query for data fetching

## Timeline Estimate

### Phase 1 (Immediate)
- **Utility functions**: 0.5 days
- **Card entry updates**: 1 day
- **Individual card view**: 0.5 days
- **Testing and refinement**: 0.5 days
- **Total**: 2.5 days

### Phase 2 (After Backend)
- **Backend API endpoint**: 1 day
- **Frontend API integration**: 0.5 days
- **Composable creation**: 0.5 days
- **Component updates**: 1 day
- **Testing**: 0.5 days
- **Total**: 3.5 days

## Risk Assessment

### Low Risk (Phase 1)
- Uses existing data
- No breaking changes
- Straightforward implementation

### Medium Risk (Phase 2)
- Requires backend coordination
- Additional API calls
- Potential performance impact

## Conclusion

This plan provides a focused approach to enhancing card engagement through progress indicators and deck membership. Phase 1 can be implemented immediately with existing data, while Phase 2 provides a roadmap for when backend support is available.

The enhancements will make cards more informative and engaging while maintaining the existing user experience and performance characteristics.