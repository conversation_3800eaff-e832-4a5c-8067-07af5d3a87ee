# Implementation Plan for ContextView2 and useDictionaryLookup2

## Current Behaviors

### ContextView.vue Behaviors:
- **Two-view system**: Simple view (with language dropdown) and detailed view (English only)  
- **Reactive lookups**: Language changes and view switches trigger automatic lookups if definitions don't exist
- **Temporary ID workflow**: Creates temporary ID (-Date.now()) immediately, then waits for server to return real ID
- **URL management**: Updates route parameters as IDs change from temporary to real
- **Watchers**: Multiple watchers for route changes (`route.params.id`), view mode (`store.isShowWordContextDetailed`), and language selection (`selectedSimpleViewLanguage`)

### useDictionaryLookup.ts Behaviors:
- **Single request tracking**: Uses `currentRequestId` to cancel outdated requests
- **Streaming with fallback**: Attempts streaming first, falls back to non-streaming on errors
- **Background server sync**: Saves/updates to server asynchronously without blocking UI
- **State consolidation**: Single `wordEntry` ref holds all definition data (simple, detailed, multi-language)

## Issues with Current Architecture

### Race Condition Problems:
- **Shared request ID**: `currentRequestId` cancels all request types, preventing concurrent streams
- **ID timing issues**: Subsequent requests may use invalid temporary ID if first request is slow
- **State overwrites**: Server updates can overwrite streaming content mid-stream

### Architectural Problems:
- **Code duplication**: Similar streaming logic repeated across multiple functions (`useDictionaryLookup.ts:49-177`)
- **Complex state management**: Multiple boolean flags (`isStreamingSimple`, `isStreamingDetailed`) create confusion
- **No stream isolation**: Cannot run simple and detailed lookups concurrently
- **Inconsistent error handling**: Different error patterns across streaming functions

## New Architecture Overview

### Core Principle: Independent Stream Management
Each lookup operation becomes an independent stream that can run concurrently without interfering with other streams. Users can switch views and languages freely while all streams continue running in the background.

### High-Level Behaviors and Flows

#### 1. **Stream Independence**
- **Simple English lookup**: Can run independently and be preserved when switching to detailed view
- **Detailed English lookup**: Can run independently and be preserved when switching to simple view  
- **Language-specific lookups**: Each language maintains its own stream, preserved when switching languages
- **View switching**: Users can switch between simple/detailed views instantly, with content preserved from previous streams
- **Language switching**: Users can switch languages in simple view instantly, with previous language content preserved

#### 2. **Lookup Trigger Flows**

**Initial Word Lookup Flow:**
1. User highlights word → Immediate UI feedback with loading state
2. Create temporary ID and update URL immediately  
3. Start appropriate stream based on current view (simple/detailed)
4. Stream content appears progressively in UI
5. Background: Save to server and get real ID
6. Update URL with real ID when available

**View Switch Flow:**

*Simple to Detailed:*
1. User switches to detailed view → Check if detailed definition exists
2. If exists: Show immediately | If not: Start detailed stream while preserving simple content
3. Both streams continue running if user switches back and forth

*Detailed to Simple:*
1. User switches to simple view → Check if simple definition exists for current language
2. If exists: Show immediately | If not: Start simple stream while preserving detailed content
3. Both streams continue running if user switches back and forth

**Language Switch Flow (Simple View):**
1. User selects new language → Check if definition exists for that language
2. If exists: Show immediately | If not: Start language-specific stream
3. Previous language content remains preserved
4. User can switch back to previous language and see preserved content

**Refresh Flow:**
1. User clicks refresh → Determine current view and language context
2. Cancel only the relevant stream type (simple/detailed + language)
3. Start fresh stream for current context
4. Preserve content from other stream types

**URL Navigation Flow (Server Hydration):**
1. User navigates to existing word URL → Disable UI buttons to prevent race conditions
2. Fetch word entry from server → Set as central state
3. Re-enable UI and check current view/language context
4. Check if current view already has definition:
   - Simple view: Check if `definitionShort.explanation` OR `definitionShort.languages[currentLanguage].explanation` exists
   - Detailed view: Check if `definition` exists
5. Start stream only if current view definition is missing → Preserve existing server data, no proactive fetching

**New Word Lookup Flow (Complete Reset):**
1. User selects new word and presses lookup → Increment all stream request IDs (soft cancellation)
2. Create new WordInContextEntry with basic info (word, context, offset) and loading states for definitions
3. Start fresh lookup flow with new word/context/offset
4. Proceed with normal Initial Word Lookup Flow

#### 3. **State Management Behaviors**

**Central State Structure:**
- Reuse existing `WordInContextEntry` type as central reactive state
- Make `definitionShort.explanation` optional to support direct language lookups
- Store simple definitions per language: `definitionShort.languages.{lang}.explanation`
- Store detailed definition in `definition` field (English only currently)
- Preserve all definitions until user navigates away

**Event-Driven Updates:**
- Streams emit semantic events rather than directly mutating state using VueUse's `useEventBus`
- Central state manager applies events as pure functions
- UI reactively updates from central `WordInContextEntry` state

**Event Types Based on Streaming API Patterns:**
```typescript
// Simple/Language streams (text appending)
{ type: 'StreamStarted', streamType: 'simple', language: 'English' }
{ type: 'SimpleTextChunk', language: 'English', chunk: 'text...' }
{ type: 'StreamCompleted', streamType: 'simple', language: 'English' }

// Detailed streams (object merging) 
{ type: 'StreamStarted', streamType: 'detailed' }
{ type: 'DetailedPartialUpdate', definition: { ... } }
{ type: 'StreamCompleted', streamType: 'detailed' }

// Error handling
{ type: 'StreamError', streamType: 'simple', language: 'Spanish', error: '...' }
```

**State Update Logic:**
- **Simple streams**: Append text chunks to `definitionShort.explanation` or `languages[lang].explanation`
- **Detailed streams**: Deep-merge the incoming partial object into `definition` (preserve earlier sections, overwrite updated ones)
- **Stream start**: Clear relevant field (handle refresh mid-stream)
- **Stream completion**: Mark stream as complete, maintain content

**Stream Request ID Management:**
- Separate request ID counters per stream type: `simpleRequestId`, `detailedRequestId`, `languageRequestId`
- Language streams use composite IDs: `${++languageRequestId}-${language}` (e.g., "3-Spanish", "4-French")
- Each stream captures its current ID at start and validates throughout streaming
- Active language requests tracked: `activeLanguageRequests.Spanish = "3-Spanish"`
- Stream chunks validate: `if (activeLanguageRequests.Spanish !== requestId) return; // ignore outdated`
- Enables true concurrent language streams while maintaining global sequence tracking
- Simple string comparison-based soft cancellation (no API cancellation calls needed)

**Server ID Coordination Behaviors:**
- First stream to complete creates server entry and obtains real ID
- Subsequent streams during ID resolution: mark `pendingServerUpdate` flag (no waiting, no individual updates)
- Streams completing after ID resolution: send update requests immediately as normal
- First stream after getting real ID: if `pendingServerUpdate` flag is set, send one consolidated update with complete central state
- All streams can write to local state immediately (optimistic updates)
- Server sync happens asynchronously without blocking UI (separate from events)

**Error Handling Behaviors:**
- Stream errors don't affect other running streams
- Users can retry failed streams while others continue
- Fallback to non-streaming if streaming fails
- Graceful degradation maintains core functionality

#### 4. **Concurrent Stream Scenarios**

**Scenario A - Fast View Switching:**
1. Start simple English lookup → User immediately switches to detailed view
2. Simple stream continues running in background
3. Detailed stream starts concurrently  
4. User sees both streams progress independently
5. User can switch back and forth seeing preserved content

**Scenario B - Language Exploration:**
1. User in simple view, English definition loads
2. User switches to Spanish → Spanish stream starts, English content preserved
3. User switches to French → French stream starts, English + Spanish preserved
4. User can cycle through languages seeing all preserved content

**Scenario C - Refresh During Multi-Stream:**
1. Simple English + Spanish streams running
2. User refreshes → Only refreshes current language stream
3. Other language content preserved
4. User switches languages and sees mix of fresh + preserved content

**Scenario D - URL Navigation with Missing Data:**
1. User navigates to existing word URL → Server returns entry with only English simple definition
2. UI buttons disabled during server fetch → Shows English definition immediately when loaded
3. User switches to Spanish → UI enabled, Spanish definition missing → Start Spanish stream
4. User switches to detailed view → Detailed definition missing → Start detailed stream
5. Both Spanish simple and detailed streams run concurrently

**Scenario E - New Word Selection During Multi-Stream:**
1. User looking up "apple" → Simple English + detailed streams running 
2. User selects new word "banana" → All stream IDs incremented (soft cancel "apple" streams)
3. Central state cleared → UI immediately shows empty content
4. "banana" lookup starts fresh → Previous "apple" stream chunks ignored when they arrive

### Architecture Benefits:
- **True concurrency**: Multiple lookups run simultaneously without cancellation
- **Instant responsiveness**: View/language switches show preserved content immediately
- **Progressive enhancement**: Content appears as streams complete, no blocking
- **Resilient to user behavior**: Fast switching doesn't break or cancel operations
- **Intuitive UX**: Users see immediate feedback and preserved content as expected

## Implementation Guidelines

### Scope and Constraints
- **Architecture-only changes**: Only change underlying logic and code architecture to create maintainable, clean, and elegant codebase
- **No UI/UX changes**: Don't change how the UI looks and feels - maintain identical user experience
- **No new UI components**: Don't create new UI components or styles - reuse existing components exactly
- **Module-based file creation**: Keep `ContextView2.vue` and `useDictionaryLookup2.ts` as entry points, but freely create small helper modules (e.g., `requestIds.ts`, `lookupState.ts`, `simpleEnglishStream.ts`, etc.) in the same folder for clarity and single-responsibility.
- **File organization**: All helper modules live alongside `ContextView2.vue` for easy discoverability; avoid scattering logic in unrelated directories
- **Complete rewrite**: Write `useDictionaryLookup2` from scratch without consulting old implementation - the current code is messy and buggy
- **Logic refactoring freedom**: Freely refactor watchers, event handlers, and other logic patterns for cleaner architecture

### Development Approach
- **Clean slate development**: Don't reference current `useDictionaryLookup.ts` implementation details - the old implementation is extremely buggy and messy
- **Start from scratch**: Only look at the old implementation for UI styling and component structure; avoid copying any logic patterns
- **Focus on architecture**: Implement the event-driven, stream-based architecture from this plan
- **Maintain behavior**: Ensure all existing user-facing behaviors work identically
- **Code quality**: Prioritize maintainability, readability, and elegance over backward compatibility with messy patterns

### Code Structure and Principles
- **File organization**: Freely create separate files if `useDictionaryLookup2.ts` becomes too long - organize by concern/responsibility
- **Functional core, imperative shell**: Follow clean architecture principles:
  - **Functional core**: Pure functions for state transformations, event processing, validation logic
  - **Imperative shell**: Managers handle side effects, mutations, API calls, stream coordination
- **Code quality standards**:
  - Clean, easy to understand, and maintainable code
  - Pure unit functions for all business logic
  - Clear separation between pure computation and side effects
  - Self-documenting code with clear function and variable names

### Migration Strategy

1. **Parallel development**: Build ContextView2 alongside existing ContextView
2. **Incremental testing**: Test stream management with simple scenarios first  
3. **Feature parity**: Ensure all existing behaviors work in new architecture
4. **A/B comparison**: Run both implementations side-by-side for validation
5. **Development testing**: Replace ContextView import with ContextView2 in the route/page for testing
6. **Gradual rollout**: Switch users to ContextView2 once stability is confirmed

This architecture eliminates race conditions, reduces code duplication, and provides clean concurrent stream management while maintaining all existing functionality.

## Flow Diagrams

### Initial Word Lookup Flow
```mermaid
sequenceDiagram
    participant User
    participant UI
    participant StateManager
    participant StreamManager
    participant API
    participant Server
    
    User->>UI: Highlights word
    UI->>StateManager: Emit StreamStarted event
    StateManager->>StateManager: Create WordInContextEntry with basic info and temp ID
    StateManager->>UI: Show word/context immediately with loading states
    UI->>User: Show immediate structured feedback
    
    StateManager->>StreamManager: Start stream (simple/detailed)
    StreamManager->>StreamManager: Capture requestId
    StreamManager->>API: Begin streaming
    
    loop Stream chunks
        API-->>StreamManager: Chunk received
        StreamManager->>StreamManager: Validate requestId
        StreamManager->>StateManager: Emit TextChunk/PartialUpdate event
        StateManager->>StateManager: Apply event to central state
        StateManager->>UI: Reactive update
        UI->>User: Progressive content display
    end
    
    API-->>StreamManager: Stream complete
    StreamManager->>StateManager: Emit StreamCompleted event
    StateManager->>Server: Save to server (background)
    Server-->>StateManager: Return real ID
    StateManager->>UI: Update URL with real ID
```

### Concurrent View Switching Flow
```mermaid
sequenceDiagram
    participant User
    participant UI
    participant StateManager
    participant SimpleStream
    participant DetailedStream
    
    Note over User,DetailedStream: Simple stream already running
    
    User->>UI: Switch to detailed view
    UI->>StateManager: Check if detailed definition exists
    StateManager->>StateManager: Definition missing
    StateManager->>DetailedStream: Start detailed stream
    DetailedStream->>DetailedStream: Capture detailedRequestId
    
    StateManager->>UI: Switch to detailed view layout
    StateManager->>UI: Show loading state (detailed content missing)
    UI->>User: Instant view switch to loading state
    
    par Simple stream continues (preserved)
        SimpleStream-->>StateManager: SimpleTextChunk event
        StateManager->>StateManager: Append to simple definition
        Note over StateManager: Simple content preserved for switch back
    and Detailed stream progresses
        DetailedStream-->>StateManager: DetailedPartialUpdate event
        StateManager->>StateManager: Merge detailed definition (deep merge)
        StateManager->>UI: Progressive detailed content updates
    end
    
    UI->>User: Progressive detailed content display
    
    Note over User,DetailedStream: If user switches back to simple view, preserved content shows immediately
```

### Language Switching with Concurrent Streams
```mermaid
sequenceDiagram
    participant User
    participant UI
    participant StateManager
    participant EnglishStream
    participant SpanishStream
    participant FrenchStream
    
    Note over User,FrenchStream: English stream running
    
    User->>UI: Select Spanish language
    UI->>StateManager: Check if Spanish definition exists
    StateManager->>StateManager: Spanish definition missing
    StateManager->>SpanishStream: Start Spanish stream
    SpanishStream->>SpanishStream: Capture requestId "2-Spanish"
    
    StateManager->>UI: Switch to Spanish language interface
    StateManager->>UI: Show loading state (Spanish content missing)
    UI->>User: Instant language switch to loading state
    
    User->>UI: Select French language (mid-Spanish stream)
    StateManager->>FrenchStream: Start French stream
    FrenchStream->>FrenchStream: Capture requestId "3-French"
    
    par English stream (if still running)
        EnglishStream-->>StateManager: SimpleTextChunk (English)
    and Spanish stream continues
        SpanishStream-->>StateManager: SimpleTextChunk (Spanish)
        StateManager->>StateManager: Validate "2-Spanish" requestId
    and French stream starts
        FrenchStream-->>StateManager: SimpleTextChunk (French)
        StateManager->>StateManager: Validate "3-French" requestId
    end
    
    StateManager->>UI: All language contents preserved
    User->>UI: Switch back to Spanish
    UI->>User: Show preserved Spanish content instantly
```

### URL Navigation Flow
```mermaid
sequenceDiagram
    participant User
    participant UI
    participant StateManager
    participant Server
    participant StreamManager
    
    User->>UI: Navigate to word URL
    UI->>UI: Disable buttons (prevent race conditions)
    UI->>Server: Fetch word entry by ID
    Server-->>UI: Return word entry
    UI->>StateManager: Set as central state
    UI->>UI: Re-enable buttons
    
    StateManager->>StateManager: Check current view context
    
    alt Simple view - definition exists for current language
        StateManager->>UI: Show existing definition
        UI->>User: Immediate content display
    else Simple view - definition missing for current language
        StateManager->>StreamManager: Start language-specific stream
        StreamManager-->>StateManager: Progressive text chunks
        StateManager->>UI: Progressive updates
    else Detailed view - definition exists
        StateManager->>UI: Show existing detailed definition
    else Detailed view - definition missing
        StateManager->>StreamManager: Start detailed stream
        StreamManager-->>StateManager: Progressive partial updates
        StateManager->>UI: Progressive updates
    end
```

### New Word Lookup Flow (Stream Cancellation)
```mermaid
sequenceDiagram
    participant User
    participant UI
    participant StateManager
    participant OldStreams
    participant NewStream
    
    Note over User,NewStream: Multiple streams running for "apple"
    
    User->>UI: Select new word "banana"
    UI->>StateManager: New word lookup request
    StateManager->>StateManager: Increment all stream request IDs
    Note over StateManager: simpleRequestId++, detailedRequestId++, languageRequestId++
    
    StateManager->>StateManager: Create new WordInContextEntry with basic info
    Note over StateManager: word: "banana", context: "...", offset: 123, definitions: loading states
    StateManager->>UI: Show word/context immediately with loading states
    UI->>User: Immediate feedback with structured content
    
    StateManager->>NewStream: Start fresh lookup for "banana"
    NewStream->>NewStream: Capture new requestIds
    
    par Old streams receive chunks
        OldStreams-->>StateManager: "apple" chunks arrive
        StateManager->>StateManager: Validate requestId (outdated)
        Note over StateManager: Ignore "apple" chunks
    and New stream progresses
        NewStream-->>StateManager: "banana" chunks arrive
        StateManager->>StateManager: Validate requestId (current)
        StateManager->>StateManager: Apply "banana" updates
        StateManager->>UI: Progressive "banana" content
    end
    
    UI->>User: Only "banana" content displayed
```

### Event-Driven State Management
```mermaid
sequenceDiagram
    participant Stream
    participant VueUseEventBus
    participant StateManager
    participant CentralState
    participant UI
    
    Stream->>VueUseEventBus: eventBus.emit('StreamStarted', event)
    VueUseEventBus->>StateManager: Trigger subscribed handler
    StateManager->>StateManager: Apply pure function
    StateManager->>CentralState: Clear relevant field
    CentralState->>UI: Reactive update
    
    Stream->>VueUseEventBus: eventBus.emit('SimpleTextChunk', event)
    VueUseEventBus->>StateManager: Trigger subscribed handler
    StateManager->>StateManager: Apply pure append function
    StateManager->>CentralState: Append to explanation
    CentralState->>UI: Reactive update
    
    Stream->>VueUseEventBus: eventBus.emit('DetailedPartialUpdate', event)
    VueUseEventBus->>StateManager: Trigger subscribed handler
    StateManager->>StateManager: Apply pure replace function
    StateManager->>CentralState: Replace definition field
    CentralState->>UI: Reactive update
    
    Stream->>VueUseEventBus: eventBus.emit('StreamCompleted', event)
    VueUseEventBus->>StateManager: Trigger subscribed handler
    StateManager->>StateManager: Apply pure function
    StateManager->>CentralState: Mark stream complete
    
    Note over Stream,UI: Server sync happens separately from events
```

### Server ID Resolution and Sync Flow
```mermaid
sequenceDiagram
    participant StreamA as First Stream
    participant StreamB as Second Stream
    participant StreamC as Third Stream
    participant StateManager
    participant IDManager
    participant Server
    participant UI
    
    Note over StreamA,UI: Multiple streams running with temp ID "-123456"
    
    StreamA->>StreamA: Stream completes first
    StreamA->>IDManager: Request server sync
    IDManager->>IDManager: Check: realId = null, idResolutionInProgress = false
    IDManager->>IDManager: Set idResolutionInProgress = true
    IDManager->>Server: Create entry
    
    par First stream creates entry
        Server-->>IDManager: Return real ID "42"
        IDManager->>IDManager: Store realId = "42", idResolutionInProgress = false
        IDManager->>StateManager: Update central state with real ID
        StateManager->>UI: Update URL with real ID
        IDManager->>IDManager: Check pendingServerUpdate flag
        alt pendingServerUpdate = true
            IDManager->>Server: Send consolidated update with complete central state
            IDManager->>IDManager: Set pendingServerUpdate = false
            Server-->>IDManager: Confirm consolidated update
        end
    and StreamB completes during ID resolution
        StreamB->>IDManager: Request server sync
        IDManager->>IDManager: Check: realId = null, idResolutionInProgress = true
        IDManager->>IDManager: Set pendingServerUpdate = true
        Note over IDManager: No waiting, no individual server request
    and StreamC completes after ID resolution
        StreamC->>IDManager: Request server sync
        IDManager->>IDManager: Check: realId = "42" (exists)
        IDManager->>Server: Send update immediately
        Server-->>IDManager: Confirm update
    end
    
    Note over StreamA,UI: Optimized: 1 create + 1 consolidated update + immediate updates
```

## Detailed Implementation Plan

### Phase 1: Basic ContextView2 Component [DONE]
**Goal**: Create minimal working ContextView2 component that can replace the original

**Deliverables**:
- Create `ContextView2.vue` with identical UI layout to original
- Create minimal `useDictionaryLookup2.ts` with basic state management
- Reuse all existing child components (`ExplanationView`, `TiptapEditor`, etc.)
- Handle route parameters and basic navigation

**Core functionality**:
- Static component structure matching original ContextView
- Basic reactive state for `wordEntry` (no streaming yet)
- Route parameter watching for URL navigation
- Placeholder methods for lookup operations

**Acceptance Criteria**:
- UI looks identical to original ContextView
- Component mounts and displays without errors
- Route navigation works (URL updates, back/forward buttons)
- All existing child components render correctly

**Testing**: Visual comparison with original, basic navigation testing

---

### Phase 2: Replace ContextView with ContextView2 [DONE]
**Goal**: Switch development environment to use new component

**Deliverables**:
- Update route/page imports to use ContextView2
- Ensure no breaking changes in basic functionality
- Side-by-side testing capability for comparison

**Acceptance Criteria**:
- Application loads and runs without errors
- No visual differences from original
- Route navigation continues working
- Easy rollback to original if needed

**Testing**: Full application functionality with new component

---

### Phase 3: Initial Lookup Streaming [DONE]
**Goal**: Implement the basic streaming functionality for a simple English lookup.

**Deliverables**:
- Implement core streaming logic for `lookupSimpleEnglish` in a dedicated helper module `simpleEnglishStream.ts`.
- Create `requestIds.ts` for centralized request-ID management utilities.
- Extract reactive state & global event-bus handling into `lookupState.ts`.
- Keep `useDictionaryLookup2.ts` lightweight; it simply orchestrates these helpers and exposes a clean API.
- Utilize the `Dictionary` class from `pickvocab-dictionary` to handle the lookup.
- Call `dictionary.getMeaningInContextShortStream` to get an async generator for the definition.
- Implement a fallback to the non-streaming `getMeaningInContextShort` if the stream fails.
- Use an event-driven approach (`useEventBus`) to emit `StreamStarted`, `SimpleTextChunk`, `StreamCompleted`, and `StreamError` events.
- Implement a basic request ID system through `requestIds.ts` to handle simple race conditions.
- All state management logic (creating the temporary entry, appending chunks, setting streaming flags) is encapsulated within **`lookupState.ts`** by listening to its own events.
- `ContextView2.vue` remains presentation-only and triggers lookups via the composable API.
- The UI reactively displays the streamed content without a loading spinner (`isLoading` is not set to `true`).

**Acceptance Criteria**:
- A new word lookup triggers a stream via the `Dictionary` class.
- Progressive text updates appear in the simple view UI.
- The `isStreamingSimple` flag correctly shows the "Exploring meaning..." message.
- The request ID validation prevents chunks from an old stream from updating the UI after a new lookup has started.
- The basic `WordInContextEntry` is created with a temporary ID (`temp-` prefix) and the correct `llm_model` ID.
- TypeScript errors are resolved.
- The 404 error is fixed by using the correct data access pattern.
- The event handling logic is cleanly located in the composable, not the component.

**Testing**:
- Verify that streaming starts on a new lookup.
- Confirm that text chunks are displayed progressively.
- Test that a quick new lookup correctly cancels the previous stream's UI updates.
- Verify that the loading spinner does not show, but the streaming indicator does.

---

### Phase 4: Server Sync and URL Management [DONE]
**Goal**: Implement the server synchronization and URL update flow after a stream completes.

**Deliverables**:
- After the first successful stream for a new word, save the `WordInContextEntry` to the server.
- The server returns a real ID for the entry.
- The application state and URL are updated with the new, real ID.
- Implement the `IDManager` logic from the "Server ID Resolution and Sync Flow" diagram to handle concurrent stream completions and consolidate updates.

**Acceptance Criteria**:
- A new word entry is created on the server upon completion of the first stream.
- The temporary ID in the URL (`/app/contextual-meaning/-12345`) is replaced with the real ID from the server (`/app/contextual-meaning/42`).
- Subsequent streams for the same entry update the existing server record.
- The `pendingServerUpdate` flag logic correctly consolidates multiple updates into one.

**Testing**:
- Check the network tab to confirm the `create` and `update` API calls.
- Verify the URL changes from temporary to real ID without a full page reload.
- Test the scenario where multiple streams complete around the same time to ensure only one `create` call is made.

---

### Phase 5: Language Support and Multi-Stream [DONE]
**Goal**: Add full multi-language support (UI + concurrent streams) immediately after verifying server sync.

**Deliverables (implemented)**:
- Reinstate the language dropdown in the simple view UI (English, Spanish, French by default).
- Implement `lookupSimpleLanguage(language)` using the composite request-ID scheme (`${requestId}-${language}`).
- Allow concurrent streaming for multiple languages without interference.
- Persist streamed content per language in central state so switching is instantaneous.
- Ensure the server-sync logic correctly handles multiple streams finishing in any order (leveraging the `pendingServerUpdate` optimisation).

**Core language features**:
```typescript
// Language-specific streaming helpers
async function lookupSimpleLanguage(language: string): Promise<void>
function generateLanguageRequestId(language: string): string
function isValidLanguageRequest(requestId: string, language: string): boolean
```

Additionally implemented improvements (not in original spec):
- Lookup now respects the *current* UI language. If the user is on Spanish view and looks up a new word, the system streams Spanish first. English is no longer required as the bootstrap stream.
- `lookupSimpleLanguage` can create the base `WordInContextEntry` if no English stream has started yet. This prevents empty states and allows one-click foreign-language lookups.
- Both English and language streaming helpers were refactored into smaller functions (`perform*Stream`, `perform*NonStreaming`, etc.) improving readability and maintainability without changing behavior.
- The "Exploring the meaning…" streaming indicator in simple view is now **language-aware**: it appears only when the *currently selected* language is actively streaming, preventing confusion when switching back to a language whose stream has finished.

**Implementation Summary for Language-Aware Streaming Indicator:**

The fix involved three key changes to make the streaming indicator language-specific:

1. **Reactive Language Tracking in `lookupState.ts`:**
   - Converted `activeSimpleLanguages` from a plain `Set<string>` to a reactive `ref<Set<string>>`
   - Since Vue doesn't track Set mutations, we clone the Set on every add/remove operation to trigger reactivity
   - Added/removed languages from the Set when streams start/complete/error
   - Exported `activeSimpleLanguages` from the state composable

2. **Computed Flag in `ContextView2.vue`:**
   - Created `isStreamingSimpleCurrentLang` computed property that checks if the currently selected language is in the `activeSimpleLanguages` Set
   - This computed combines the global `isStreamingSimple` flag with language-specific tracking
   - Passed this computed value to `ExplanationView` instead of the global flag

3. **Composable API Update in `useDictionaryLookup2.ts`:**
   - Exposed `activeSimpleLanguages` through the composable return object
   - This allows components to observe which languages are actively streaming

**Result:** When French is streaming and user switches to English view, the indicator disappears because English is not in the `activeSimpleLanguages` Set. The indicator only appears when the currently-viewed language is actively streaming.

**Acceptance Criteria (all met)**:
- Language dropdown operates visually and functionally.
- Starting a lookup in one language while another is in progress runs both streams concurrently.
- Switching languages shows preserved, progressively updated content with no flicker.
- Server-side: first completed stream triggers `create`, others trigger one consolidated `update` per `pendingServerUpdate` logic.

**Testing**:
- Start English lookup, then quickly add Spanish and French.
- Observe three parallel streams in network logs; verify UI updates correctly.
- Confirm only one `POST /word_in_context/` occurs, followed by a single consolidated `PUT`.
- Rapidly switch languages to ensure UI preserves content and no race conditions occur.

---

### Phase 6: Detailed View Implementation [DONE]
**Goal**: Add detailed view support with full concurrency.

**Deliverables**:
- Implement the streaming logic for the detailed view (`lookupDetailed`).
- Implement view switching logic between simple and detailed views.
- Ensure simple and detailed streams can run concurrently.
- Preserve content for both views when switching back and forth.
- Ensure the detailed lookup **uses the streaming API**: call `dictionary.getMeaningInContextStream(word, context, offset)` to receive progressive chunks.
- While streaming, **merge** each partial `definition` into state instead of replacing:

```typescript
for await (const { chunk, isComplete, result } of dictionary.getMeaningInContextStream(word, context, offset)) {
  if (result?.definition) {
    currentEntry.value.definition = {
      ...currentEntry.value.definition,   // keep what we already have
      ...result.definition,               // add / overwrite the new parts
    };
  }
  // update UI with chunk, handle completion, etc.
}
```

**Core detailed view features**:
```typescript
// Detailed view streaming
async function lookupDetailed(): Promise<void>
async function refreshDetailed(): Promise<void>
function hasDetailedDefinition(state: WordInContextEntry): boolean
```

**Implemented highlights**:
- Added `lookupDetailed` stream helper with request-ID handling and streaming + fallback.
- `DetailedPartialUpdate` event now emits `definition` patch directly (no `partialEntry` wrapper).
- State layer merges `definition` patches; streaming flag `isStreamingDetailed` handled.
- View-switch watcher triggers missing streams; unified helper `ensureSimpleDefinition` removed duplication.
- Concurrency verified between simple & detailed streams.

**Acceptance Criteria**:
- Detailed view streaming works and progressively updates the UI.
- Switching from simple to detailed view (and back) is instant and preserves the content of both views.
- A simple stream and a detailed stream can run concurrently for the same word without interference.
- All original features of the detailed view work as expected.

**Testing**: Test view-switching scenarios, including switching while streams are in progress. Verify content preservation and concurrent stream behavior.

### Phase 7: Refresh Stream (All Views & Languages) [DONE]
**Goal**: Implement the refresh functionality for the CURRENT VIEW CONTEXT (simple/detailed + selected language).

**Deliverables**:
- Implement a generic `refreshCurrentContext` function that derives the current "stream descriptor" (e.g. `kind: "simple", lang: "es"` or `kind: "detailed"`) from the UI.
- When the user clicks refresh, cancel only the stream that matches that descriptor by incrementing its request-id.
- Clear just the matching definition field(s) in the central state and start a fresh stream with the new request-id.
- Preserve content and in-flight work for all other streams.

**Acceptance Criteria**:
- Clicking refresh restarts ONLY the definition currently being viewed and leaves all other definitions intact.
- Works in simple English, simple language *X*, and detailed views.
- UI shows the expected loading / streaming indicator and then progressively displays the new content.

**Testing**:
- Refresh in simple English while other languages are streaming.
- Refresh in a non-English language.
- Refresh in detailed view with a simple stream running in parallel.

---

### Phase 8: URL Navigation (Server Hydration) [DONE]
**Goal**: Implement the behavior for loading a word definition directly from a URL.

**Deliverables**:
- When a user navigates to a URL with a real word ID, fetch the full `WordInContextEntry` from the server.
- Populate the central state with the server data.
- The UI should display the fetched content immediately.
- If the specific definition for the current view/language is missing from the server data, a new stream should be automatically triggered to fetch it.

**Implementation**:
- Route watcher added to `ContextView2.vue` (not the composable) to handle URL-based hydration.
- Uses `RemoteWordInContextApi.get()` to fetch entries by numeric ID.
- Sets `isLoading` during fetch to disable UI controls and prevent race conditions.
- Watcher on `wordEntry` triggers appropriate streams for missing definitions after hydration.
- Ignores temporary IDs and avoids redundant fetches.

**Acceptance Criteria**:
- Navigating to `/app/contextual-meaning/42` loads the data for word 42 from the server.
- The UI is populated correctly with the hydrated data.
- UI controls are disabled during the initial fetch to prevent race conditions.
- If the user switches to a view/language for which data doesn't exist, a stream is automatically started.

**Testing**:
- Look up a word to get a real ID, then copy the URL.
- Paste the URL into a new tab and verify the word loads correctly from the server.
- Test with entries that have partial data (e.g., only simple English) to ensure streams are triggered correctly for missing data.

---

### Phase 9: New Word Lookup (Mid-Stream Cancellation) [DONE]
**Goal**: Ensure a new word lookup correctly cancels all ongoing streams for the previous word.

**Deliverables**:
- Implement the `handleNewWordLookup` function.
- When a new word is looked up, increment all stream request IDs (simple, detailed, language). This serves as a "soft cancellation" for all in-flight operations for the previous word.
- The central state should be immediately cleared and replaced with a new `WordInContextEntry` for the new word, showing loading states.
- A fresh lookup flow for the new word is initiated.

**Acceptance Criteria**:
- Looking up a new word while another lookup is in progress immediately clears the UI and shows the new word's context.
- Any arriving chunks from the previous word's streams are ignored.
- The UI progressively displays content only for the new word.

**Testing**:
- Start a lookup for a word.
- While it's streaming, quickly look up a different word.
- Verify the UI switches immediately and that no content from the first word appears.

---

### Phase 10: Multi-Concurrent Stream Validation [DONE]
**Goal**: Test complex concurrent streaming scenarios involving multiple languages.

**Test scenarios**:
- **Language exploration**: Switch between multiple languages rapidly to test UI responsiveness and state preservation.
- **Concurrent lookups**: Have multiple language streams running simultaneously and verify correctness.
- **Create/update race conditions**: Test the server sync logic when multiple language streams complete during the initial ID resolution phase.
- **Mixed timing**: Ensure the system correctly handles streams completing at different times and in different orders.

**Acceptance Criteria**:
- All concurrent scenarios work without race conditions or state corruption.
- The server sync logic correctly optimizes API calls (e.g., one create, one consolidated update).
- Memory usage remains reasonable, and there are no stream leaks.

**Testing**: Extensive manual testing of complex multi-stream scenarios, monitoring network traffic and app performance.

---

---

### Phase 11: Complete Flow Integration
**Goal**: Integrate all features and validate complete user flows from end-to-end.

**Deliverables**:
- A single, cohesive system where all streaming types (simple, language, detailed) work together.
- Full user flow testing across all features.
- Robust edge case handling and error recovery.

**Complete feature set**:
- Simple view with multiple concurrent language support.
- Detailed view (English only) with concurrent simple/detailed streaming.
- View and language switching with content preservation.
- URL navigation and server hydration.
- Refresh functionality for all stream types.
- New word lookup with proper cancellation of all previous streams.

**Acceptance Criteria**:
- All user flows work smoothly and intuitively.
- There are no feature regressions compared to the original implementation.
- The final code architecture is clean, maintainable, and easy to understand.
- The application gracefully handles API or streaming errors.

**Testing**: Complete user journey testing, regression testing against the original component, and "chaos testing" (e.g., rapid clicking, switching, and refreshing).

---
