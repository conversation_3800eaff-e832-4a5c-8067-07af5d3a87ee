# LookupModal2 Migration Guide

This document explains why **LookupModal2.vue** was introduced, how it differs from the legacy modal, and what changes were required across the code-base.

---

## 1. Background & Problems with `LookupModal.vue`

`LookupModal.vue` relied on the **old** composable `useDictionaryLookup`.  That implementation:

* Managed only a single request ID, so concurrent streams (simple vs detailed vs language) could not run together.
* Assigned a temporary **negative-number** ID (`-Date.now()`) while waiting for the server response.  Because the value was numeric, the ID-manager assumed it was _already real_ and tried to `PUT /word_in_context/{tempId}/`, resulting in 404s.
* Contained duplicated logic that was later re-architected in `ContextView2`.

While `ContextView2` was migrated to the new concurrent-stream architecture (see `contextview2-architecture-plan.md`), the EPUB reader still opened the legacy modal, causing:

* 404s for the very first stream completion
* race conditions when switching languages / detailed view inside the modal

---

## 2. Solution Overview

1. **New Component** – `LookupModal2.vue`:
   * Built on the new composable `useDictionaryLookup2` (same concurrent architecture used by `ContextView2`).
   * Uses string-based temporary IDs (`"temp-${Date.now()}`") so the ID-manager can detect when the entry is not yet persisted.
   * Shares `ExplanationView` and overall UI with the former component to preserve UX.

2. **Configurable URL Sync**:
   * The ID-manager originally updated the browser route (`router.replace`) after receiving the real ID.  This makes sense in `ContextView2`, but **not** inside the modal (the reader page has no concept of `/app/contextual-meaning/:id`).
   * `idManager.ts` now accepts `options.updateUrl` (default `false`).
   * `useDictionaryLookup2` passes this flag through.
   * `ContextView2.vue` opts-in (`updateUrl: true`) ; `LookupModal2.vue` uses the default (`false`).

3. **Reader Components Updated**
   * `pages/app/read-book.vue`
   * `components/app/epubReader/StandaloneEpubReader.vue`
   were re-wired:

   ```diff
   - import LookupModal from '~/components/app/epubReader/LookupModal.vue'
   + import LookupModal from '~/components/app/epubReader/LookupModal2.vue'
   ```

   No template changes were necessary.

4. **Card Creation Fix**
   * `addCard` previously instantiated `useCreateContextCard()` inside the handler, which violates Vue-Query’s rule that hooks must run at setup-time.  The mutation is now created once at module scope and reused, mirroring the pattern in `ContextView`.

---

## 3. File-level Changes

| File | Key edits |
|------|-----------|
| `components/app/epubReader/LookupModal2.vue` | New component (stream architecture, card mutation fix) |
| `components/app/contextualMeaning/ContextView2/idManager.ts` | Added `options.updateUrl` flag |
| `components/app/contextualMeaning/ContextView2/useDictionaryLookup2.ts` | Accepts & forwards `updateUrl` option |
| `ContextView2.vue` | Calls `useDictionaryLookup2({ updateUrl: true })` |
| `read-book.vue`, `StandaloneEpubReader.vue` | Switch import to `LookupModal2.vue` |

---

## 4. Behavioural Parity Tests

* **Simple vs Detailed streams**: Concurrent streaming works; switching views no longer cancels ongoing streams.
* **Language dropdown**: Each language streams independently; indicator only shows for the active language.
* **Server sync**: First stream sends `POST`, subsequent streams consolidate `PUT` operations; no more 404s.
* **URL stability in reader**: Reading URL remains unchanged while still persisting entries server-side.
* **Card saving**: “Save” button now succeeds from within the modal (no injection-context error).

---

## 5. Migration Checklist (for future deployments)

- [x] Ensure all components opening a word-in-context modal import **LookupModal2.vue**.
- [x] Remove obsolete `LookupModal.vue` after verifying no remaining references.
- [x] Confirm server migrations (if any) handle `definition_short` -> `definitionShort` naming alignment.

---

> **Next steps:**
> * Delete the legacy `useDictionaryLookup.ts` and `LookupModal.vue` after a grace period.
> * Consider moving shared stream helpers to a dedicated composable package for reuse across web-extension and future views. 