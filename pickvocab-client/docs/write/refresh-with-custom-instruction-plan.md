# Refresh with Custom User Instruction - Implementation Plan

## High-Level Flow

### Standard Refresh Flow (Current)
```
User clicks refresh button 
→ OutputSectionHeader emits 'refreshRevision' 
→ useWriteRevisionHandler.refreshRevision() 
→ Routes to vocabulary/non-vocabulary path 
→ Generates new revision with existing prompts 
→ Appends to revision list 
→ Updates UI with new revision
```

### Custom Instruction Refresh Flow (New)
```
User clicks dropdown arrow on refresh button 
→ Selects "Refresh with instruction..." 
→ CustomInstructionDialog opens 
→ User enters custom instruction (e.g., "make it shorter") 
→ User clicks "Apply" 
→ Dialog closes, instruction passed to refresh handler 
→ useWriteRevisionHandler.refreshRevision(customInstruction) 
→ Routes to vocabulary/non-vocabulary path with custom instruction 
→ Custom instruction integrated into existing prompts 
→ Generates new revision following custom instruction 
→ Appends to revision list 
→ Updates UI with new revision
```

### UI State Flow
```
Initial State: Single refresh button
↓
User Implementation: Split button (main + dropdown)
├── Main button click → Standard refresh (current behavior)
└── Dropdown click → Custom instruction options
    └── "Refresh with instruction..." → Opens dialog
        ├── User enters instruction → Apply → Custom refresh
        ├── User selects recent instruction → Apply → Custom refresh
        └── Cancel → Returns to previous state
```

### Data Flow Integration
```
Custom Instruction Input
├── Validation (length, content)
├── Save to recent instructions (localStorage)
└── Pass to refresh handler
    ├── With vocabulary: refreshVocabularyPrompt(existing_params, customInstruction)
    └── Without vocabulary: refreshRevisionPrompt(existing_params, customInstruction)
        └── Prompt templates append instruction to existing logic
            └── LLM generates revision following both existing rules + custom instruction
```

## Current State Analysis

### Existing Refresh Functionality
The writing assistant currently supports refresh functionality through:

**Refresh Button Location**: `components/app/write/OutputSectionHeader.vue`
- Simple button that emits `refreshRevision` event
- Disabled during streaming operations
- Uses IconRefresh for visual indication

**Core Handler**: `components/app/write/useWriteRevisionHandler.ts`
- `refreshRevision()` function (lines 717-738)
- Routes to vocabulary or non-vocabulary refresh paths
- Maintains same trigger type and tone as original

**Prompt Templates**:
- `refreshVocabularyPrompt.ts` - For vocabulary-enabled refresh
- `refreshRevisionPrompt.ts` - For basic refresh without vocabulary

### Current User Input Patterns
The writing interface already handles user input through:
- Main text input textarea in `InputSection.vue`
- Tone selector with predefined and custom tones
- Vocabulary toggle and grammar check options
- All settings persisted to localStorage

## Feature Requirements

### Two Refresh Modes
1. **Standard Refresh** (current behavior)
   - No user input required
   - Uses existing prompts and logic
   - Maintains current button behavior

2. **Custom Instruction Refresh** (new feature)
   - Allows user to input custom instruction
   - Examples: "make it shorter", "make it more persuasive", "don't use the word..."
   - Integrates custom instruction into refresh prompts

## Implementation Plan

### 1. UI/UX Changes

#### A. Modify Refresh Button
**File**: `components/app/write/OutputSectionHeader.vue`

**Current**: Single refresh button
**New**: Dropdown or split button with two options:
- "Refresh" (standard)
- "Refresh with instruction..." (opens input dialog)

**Implementation Options**:
- **Option 1**: Split button (main button = standard refresh, dropdown arrow = custom)
- **Option 2**: Single button with right-click context menu
- **Option 3**: Always show input field below button (expandable)

#### B. Create Custom Instruction Input Component
**New File**: `components/app/write/CustomInstructionDialog.vue`

**Features**:
- Modal dialog or expandable section
- Textarea for custom instruction input
- Character limit (e.g., 200 chars)
- Examples/suggestions for common instructions
- "Apply" and "Cancel" buttons
- Remember recent instructions (localStorage)

### 2. State Management Updates

#### A. Add New State Variables
**File**: `useWriteRevisionHandler.ts`

```typescript
// New state variables needed
const customInstruction = ref<string>('')
const isCustomInstructionMode = ref<boolean>(false)
const recentCustomInstructions = ref<string[]>([])
```

#### B. Modify Refresh Function
**Current**: `refreshRevision()`
**New**: `refreshRevision(customInstruction?: string)`

- If `customInstruction` provided → use custom refresh path
- If not provided → use standard refresh path (current behavior)

### 3. Prompt Template Updates

#### A. Modify Existing Prompt Files
**Files to Update**:
- `refreshVocabularyPrompt.ts`
- `refreshRevisionPrompt.ts`

**Changes**:
- Add optional `customInstruction` parameter
- Integrate custom instruction into prompt when provided
- Maintain all existing logic for revision analysis and vocabulary handling

**Example Integration**:
```typescript
export function refreshVocabularyPrompt(
  // ... existing parameters
  customInstruction?: string
) {
  let prompt = `[existing prompt content]`
  
  if (customInstruction) {
    prompt += `\n\nIMPORTANT: For this revision, please specifically: ${customInstruction}`
  }
  
  return prompt
}
```

#### B. Prompt Engineering Considerations
- Ensure custom instructions don't override core requirements
- Maintain tone consistency unless instruction specifically requests change
- Handle conflicting instructions gracefully
- Preserve vocabulary integration when applicable

### 4. New Handler Functions

#### A. Custom Instruction Refresh Handlers
**File**: `useWriteRevisionHandler.ts`

```typescript
// New functions to implement
const refreshRevisionWithCustomInstruction = async (instruction: string) => {
  // Similar to existing refresh but passes custom instruction to prompts
}

const showCustomInstructionDialog = () => {
  // Opens input dialog and handles user interaction
}

const saveRecentInstruction = (instruction: string) => {
  // Saves to localStorage for quick access
}
```

### 5. Integration Points

#### A. Event Flow Updates
1. **Standard Refresh**: Unchanged - direct call to `refreshRevision()`
2. **Custom Refresh**: 
   - Show custom instruction dialog
   - User inputs instruction
   - Call `refreshRevision(customInstruction)`

#### B. Error Handling
- Validate custom instruction (not empty, reasonable length)
- Handle API errors with custom instructions
- Provide clear feedback for instruction-related issues

#### C. Accessibility
- Proper ARIA labels for new UI elements
- Keyboard navigation support
- Screen reader compatibility

### 6. Implementation Steps

#### Phase 1: Core Infrastructure
1. Add state variables to revision handler
2. Create custom instruction input component
3. Modify refresh button UI (dropdown/split button)
4. Update event handlers for both refresh types

#### Phase 2: Prompt Integration
1. Update both prompt template functions
2. Add custom instruction parameter handling
3. Test prompt behavior with various instructions
4. Ensure vocabulary integration still works

#### Phase 3: UX Enhancements
1. Add recent instructions dropdown
2. Provide instruction examples/suggestions
3. Add instruction validation and character limits
4. Implement localStorage persistence

#### Phase 4: Testing & Polish
1. Test all refresh scenarios (with/without vocabulary)
2. Test various custom instructions
3. Ensure streaming still works correctly
4. Add error handling and user feedback

### 7. Technical Considerations

#### A. Backward Compatibility
- Existing refresh functionality must remain unchanged
- All current refresh behavior preserved as default
- No breaking changes to existing API calls

#### B. Performance
- Custom instruction dialog should load quickly
- No impact on standard refresh performance
- Efficient localStorage operations for recent instructions

#### C. User Experience
- Clear distinction between refresh types
- Intuitive UI that doesn't overwhelm users
- Good defaults and helpful suggestions
- Smooth transition between modes

### 8. Future Enhancements

#### A. Instruction Templates
- Predefined instruction templates (e.g., "Make shorter", "Add examples")
- User-created instruction templates
- Category-based instruction organization

#### B. Instruction History
- Track instruction effectiveness
- Suggest improvements based on usage patterns
- Analytics on most popular instructions

#### C. AI-Suggested Instructions
- Based on content analysis, suggest relevant instructions
- Context-aware instruction recommendations
- Integration with existing tone and style analysis

## Files to Create/Modify

### New Files
- `components/app/write/CustomInstructionDialog.vue`
- `components/app/write/RefreshDropdown.vue` (if using dropdown approach)

### Modified Files
- `components/app/write/OutputSectionHeader.vue` - Update refresh button UI
- `components/app/write/useWriteRevisionHandler.ts` - Add custom instruction handling
- `components/app/write/refreshVocabularyPrompt.ts` - Add custom instruction parameter
- `components/app/write/refreshRevisionPrompt.ts` - Add custom instruction parameter

### Supporting Changes
- Update types/interfaces for custom instruction support
- Add localStorage utilities for instruction persistence
- Update tests for new functionality

This plan provides a comprehensive approach to adding custom instruction support while maintaining all existing functionality and following established patterns in the codebase.

## Implementation Status - COMPLETED ✅

### Summary
The refresh with custom instruction feature has been successfully implemented with the following key components:

### 🎯 Completed Features

#### 1. UI Components ✅
- **CustomInstructionDialog.vue**: Complete modal dialog for custom instruction input
  - Elegant gradient header design with emerald theme
  - Textarea input for custom instructions  
  - Recent instructions dropdown with localStorage persistence (max 5)
  - Example instruction buttons for common use cases
  - Revision context display showing which revision will be modified
  - Responsive design with proper mobile support
  - Character validation and real-time feedback
  - Keyboard shortcuts (Ctrl/Cmd+Enter to apply)

#### 2. Split Button Implementation ✅
- **OutputSectionHeader.vue**: Modified to support dropdown refresh options
  - Main refresh button preserves existing behavior
  - Dropdown arrow reveals "Refresh with instruction..." option
  - Seamless integration with existing UI patterns
  - Proper loading states and disabled states during operations

#### 3. Backend Integration ✅
- **useWriteRevisionHandler.ts**: Enhanced to support custom instructions
  - `refreshRevision()` function accepts optional `customInstruction` parameter
  - Smart routing to custom instruction prompts when instruction provided
  - Uses currently viewed revision (not always last revision) as input context
  - Maintains all existing refresh functionality as default behavior

#### 4. Custom Instruction Prompts ✅
- **customInstructionVocabularyPrompt.ts**: Dedicated prompt for vocabulary-enabled custom instructions
  - Focuses on precisely following user instruction rather than stylistic variety
  - Integrates vocabulary words naturally while following custom instruction
  - Provides detailed feedback on how instruction was applied
  - Outputs parser-compatible "# Revision X" format

- **customInstructionRevisionPrompt.ts**: Dedicated prompt for non-vocabulary custom instructions
  - Mirror functionality without vocabulary integration
  - Same precision-focused approach over variety
  - Consistent output format for parser compatibility

#### 5. Architectural Decisions ✅
- **Separation of Concerns**: Custom instruction refresh uses dedicated prompts instead of modifying existing refresh prompts
- **Parser Compatibility**: Custom instruction prompts output standard "# Revision X" format for seamless parsing
- **Context Awareness**: Shows user which revision the instruction will be applied to
- **Persistence**: Recent instructions saved to localStorage for quick reuse

#### 6. User Experience Enhancements ✅
- **Input Clearing**: Custom instruction dialog clears input when reopened
- **Word Limit Removal**: No character restrictions on custom instructions
- **Example Instructions**: Diverse examples beyond "make it more..." patterns
- **Revision Context**: Clear indication of which revision will be modified
- **Loading States**: Proper feedback during instruction application

### 🔧 Technical Implementation Details

#### Component Hierarchy
```
OutputSection.vue
├── OutputSectionHeader.vue (split button)
│   ├── Standard refresh button (main)
│   └── Dropdown with CustomInstructionDialog
└── CustomInstructionDialog.vue (modal)
    ├── Instruction input textarea
    ├── Recent instructions dropdown
    ├── Example instruction buttons
    └── Apply/Cancel actions
```

#### Prompt Architecture
```
useWriteRevisionHandler.refreshRevision(customInstruction?)
├── If customInstruction provided:
│   ├── customInstructionVocabularyPrompt() (with vocabulary)
│   └── customInstructionRevisionPrompt() (without vocabulary)
└── If no customInstruction:
    ├── refreshVocabularyPrompt() (existing)
    └── refreshRevisionPrompt() (existing)
```

#### Data Flow
```
User Input → CustomInstructionDialog → Apply Event → 
useWriteRevisionHandler → Custom Prompt Selection → 
LLM API Call → Markdown Parser → Revision Display
```

### 🎉 Key Achievements

1. **Zero Breaking Changes**: All existing refresh functionality preserved
2. **Architectural Separation**: Custom instructions use dedicated prompts for better control
3. **Parser Compatibility**: No changes needed to existing markdown parsing logic
4. **User-Centric Design**: Clear context, helpful examples, and persistent recent instructions
5. **Mobile Responsive**: Proper responsive design across all screen sizes
6. **Performance Optimized**: No impact on existing refresh performance

### 📁 Files Created/Modified

#### New Files:
- `components/app/write/CustomInstructionDialog.vue`
- `components/app/write/customInstructionVocabularyPrompt.ts`
- `components/app/write/customInstructionRevisionPrompt.ts`

#### Modified Files:
- `components/app/write/OutputSectionHeader.vue` - Split button implementation
- `components/app/write/useWriteRevisionHandler.ts` - Custom instruction support
- `components/app/write/OutputSection.vue` - Event handling updates
- `pages/app/write/index.vue` - Integration updates

### 🧪 Testing Scenarios Validated

- ✅ Standard refresh works unchanged
- ✅ Custom instruction refresh with vocabulary integration
- ✅ Custom instruction refresh without vocabulary
- ✅ Recent instructions persistence and retrieval
- ✅ Example instruction button functionality
- ✅ Proper revision context display
- ✅ Parser compatibility with custom instruction output
- ✅ Mobile responsiveness and accessibility
- ✅ Loading states and error handling
- ✅ Keyboard navigation and shortcuts

The implementation successfully delivers the planned functionality while exceeding the original requirements with enhanced UX features and robust architectural decisions.