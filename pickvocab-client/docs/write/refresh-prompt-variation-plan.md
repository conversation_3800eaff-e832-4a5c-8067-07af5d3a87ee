# Plan: Implement Varied Refresh Prompts for Writing Revision - ✅ COMPLETED

## Problem
Currently, the refresh button triggers the same revision process, resulting in similar outputs. We need to add variation to refresh prompts to generate different writing styles/approaches while maintaining the same tone configuration.

## Solution Overview
Create new prompt functions that instruct the LLM to generate a different style/approach compared to ALL previous revisions, while keeping the same tone but varying the writing style (structure, word choice, sentence patterns, etc.).

## Implementation Plan

### 1. Create Refresh Prompt Function ✅
- **File**: `/Users/<USER>/code/pickvocab/pickvocab-client/components/app/write/refreshRevisionPrompt.ts`
- **Purpose**: Generate prompts that encourage style variation while maintaining tone
- **Key Features**:
  - Use same `selectedToneStyleDescription` as original prompts
  - Add instructions to vary writing style compared to ALL previous revisions
  - Include specific style variation suggestions (concise vs elaborate sentences, active vs passive voice, etc.)
  - Maintain same output format as `reviseTextSimpleMarkdownPrompt.ts`
  - Dynamically generate correct revision number (`# Revision ${allPreviousRevisions.length + 1}`)

### 2. Create Refresh Vocabulary Prompt Function ✅
- **File**: `/Users/<USER>/code/pickvocab/pickvocab-client/components/app/write/refreshVocabularyPrompt.ts`
- **Purpose**: Generate refresh prompts with vocabulary integration
- **Key Features**:
  - Similar to `useMyVocabularyMarkdownPrompt.ts` but with style variation instructions
  - Keep same tone configuration
  - Encourage different vocabulary word selection/usage patterns compared to ALL previous revisions
  - Vary sentence structures and paragraph organization
  - Dynamically generate correct revision number

### 3. Update useWriteRevisionHandler ✅
- **File**: `/Users/<USER>/code/pickvocab/pickvocab-client/components/app/write/useWriteRevisionHandler.ts`
- **Changes**:
  - Created new refresh-specific functions: `initiateRefreshRevisionWithSearchStreaming()` and `initiateRefreshRevisionDirectlyStreaming()`
  - Modified `refreshRevision` function to use new refresh prompts instead of original prompts
  - Keep same trigger type (no separate "Refresh" type) to maintain history consistency
  - Pass ALL previous revisions to refresh prompts for better context

### 4. Style Variation Strategies
The refresh prompts will instruct the LLM to:
- **Keep the same tone** but vary:
  - Sentence length and complexity
  - Paragraph structure and organization
  - Word choice variety (synonyms, different expressions)
  - Transitions and flow patterns
  - Active vs passive voice usage
  - Emphasis and focus points

## Key Features of New Refresh Prompts

1. **Same Tone, Different Style**:
   - Maintain `selectedToneStyleDescription` exactly as configured
   - Vary structural and stylistic elements instead

2. **Style Variation Instructions**:
   - Specific guidance on varying sentence patterns
   - Different approaches to paragraph organization
   - Alternative word choices while maintaining meaning

3. **Consistent Output Format**:
   - Same markdown structure as existing prompts
   - Compatible with current parsing logic

4. **Vocabulary Integration**:
   - Same vocabulary pool but different usage patterns
   - Varied placement and context for vocabulary words

## Files to Create/Modify

**New Files:**
- `refreshRevisionPrompt.ts` - Basic refresh prompt with style variation
- `refreshVocabularyPrompt.ts` - Vocabulary refresh prompt with style variation

**Modified Files:**
- `useWriteRevisionHandler.ts` - Use refresh prompts in refresh flow

## ✅ IMPLEMENTED FEATURES

### Revision Management
- **Append Revisions**: Refresh adds new revisions to the list instead of replacing current ones
- **Auto-Navigation**: Users automatically switch to the newest revision when refresh starts
- **Correct Numbering**: Revision headers dynamically show correct numbers (Revision 2, 3, 4, etc.)
- **Complete History**: All revisions (original + refreshed) are saved to the same history entry

### Enhanced Context
- **All Previous Revisions**: LLM receives ALL previous revisions, not just the current one
- **Better Variation**: With complete context, LLM can create genuinely different approaches
- **Avoid Repetition**: LLM can see all attempted styles and avoid duplicating them

### UX Improvements
- **Immediate Feedback**: UI switches to new revision as soon as streaming starts
- **Real-time Streaming**: Users watch new revisions being generated in real-time
- **Proper Restoration**: When restoring from history, all revisions are available and navigable

## Expected Outcome ✅ ACHIEVED
Users get genuinely different revision styles when clicking refresh while maintaining their selected tone, providing varied structural and stylistic approaches to their writing.

## ✅ Additional UX Improvements Completed

### ✅ Enhanced Placeholder Text
- **Files**: `components/app/write/RevisedTextViewer.vue`
- **✅ Updated**: Placeholder text to be more engaging and user-friendly
- **✅ Purpose**: Better language learning app experience

### ✅ Granular Progress Tracking
- **Files**: `components/app/write/OutputSection.vue`, `useWriteRevisionHandler.ts`
- **✅ Added**: `currentStreamingSection` tracking for different generation phases
- **✅ Implemented**: Dynamic progress messages for vocabulary search, feedback, and revision phases
- **✅ Purpose**: Users understand exactly which phase is running

### ✅ Refresh Button State Management
- **Files**: `components/app/write/OutputSectionHeader.vue`
- **✅ Added**: `isRefreshing` prop to disable refresh button during streaming
- **✅ Simplified**: Clean disabled state without spinner for better UX
- **✅ Purpose**: Prevents multiple refresh clicks and provides clear feedback

### ✅ Client-Extension Parity
- **✅ Achievement**: Complete UX consistency between client and extension
- **✅ Implementation**: All improvements applied to both platforms
- **✅ Result**: Unified writing assistant experience across all platforms

## Implementation Details

### Refresh Prompt Strategy ✅ IMPLEMENTED
The refresh prompts include instructions like:
- "Provide a different stylistic approach compared to ALL previous revisions"
- "Analyze ALL previous revisions to understand what stylistic approaches have already been used"
- "Vary sentence structure, word choice, and paragraph organization"
- "Use alternative expressions and transitions"
- "Focus on different aspects of the content while keeping the same overall message"

### Integration Points ✅ COMPLETED
- ✅ Detect when `refreshRevision()` is called vs `initiateRevision()`
- ✅ Use different prompt generation functions based on the trigger type
- ✅ Maintain all existing functionality (streaming, vocabulary, error handling)
- ✅ Preserve the same output format for consistent parsing
- ✅ Added streaming support with real-time revision appending
- ✅ Integrated complete history management
- ✅ Fixed history restoration to include `currentHistoryEntryId`

## Key Technical Implementation Details

### Streaming with Append Logic
```typescript
// Add placeholder revision immediately for correct UI count
const placeholderRevision: RevisionData = { /* empty revision */ };
allRevisionsResult.value = [...existingRevisions, placeholderRevision];
currentRevisionIndex.value = existingRevisions.length; // Switch immediately

// Replace placeholder with actual streaming content
allRevisionsResult.value = [...existingRevisions, ...newRevisions];
```

### History Management
```typescript
// Initial revision: create new history entry
if (!appendToExisting) {
  await saveInitialRevisionHistory();
} else {
  // Refresh: update existing history with all revisions
  await updateHistoryWithHighlights();
}

// Restore: set currentHistoryEntryId for future updates
currentHistoryEntryId.value = historyEntry.id || null;
```

## ✅ Complete Implementation Status

The refresh functionality is now **fully implemented** with:
- 🌟 **Varied refresh prompts** that generate different stylistic approaches
- 🔄 **Proper history management** with revision appending
- 📝 **Real-time streaming** for refresh revisions
- 🎯 **Enhanced UX** with progress tracking and button states
- 🏗️ **Client-extension parity** with unified experience
- 🚀 **Production-ready** with comprehensive error handling

**Status**: ✅ **FULLY COMPLETED** with additional UX enhancements

### Enhanced Prompt Context
```typescript
// Pass ALL previous revisions instead of just current one
const allPreviousRevisions = allRevisionsResult.value.map(rev => 
  rev.originalRevision || rev.revision
);
const prompt = refreshRevisionPrompt(tone, userText, allPreviousRevisions);
```