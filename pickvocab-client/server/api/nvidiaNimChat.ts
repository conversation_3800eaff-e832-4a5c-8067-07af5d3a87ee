import { NvidiaNimChat } from "pickvocab-dictionary";

export default defineEventHandler(async (event) => {
  // Handle CORS
  // Allow all origins
    setHeader(event, 'Access-Control-Allow-Origin', '*');

    // Other headers you might need:
    setHeader(event, 'Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    setHeader(event, 'Access-Control-Allow-Headers', 'Content-Type, Authorization');
    setHeader(event, 'Access-Control-Allow-Credentials', 'true'); // If you need to send cookies

    // Handle preflight requests (OPTIONS)
    if (event.node.req.method === 'OPTIONS') {
      event.node.res.statusCode = 204;
      event.node.res.end();
      return;
    }

  const body = await readBody(event);
  const { apiKey, modelId, modelName, maxToken, endpoint, args, isThinkingModel } = body;

  const chat = new NvidiaNimChat({
    modelName,
    modelId,
    maxToken,
    apiKey,
    isThinkingModel,
  });

  if (endpoint === 'sendMessage') {
    const [messages, message] = args;
    chat.setHistory(messages);
    const response = await chat.sendMessage(message);
    return response;
  }

  if (endpoint === 'sendMessageStream') {
    const [messages, message] = args;
    chat.setHistory(messages);
    
    // Set up Server-Sent Events
    setHeader(event, 'Content-Type', 'text/event-stream');
    setHeader(event, 'Cache-Control', 'no-cache');
    setHeader(event, 'Connection', 'keep-alive');
    
    // Stream the response
    if (chat.sendMessageStream) {
      for await (const { chunk, isComplete, result } of chat.sendMessageStream(message)) {
        // Send chunk as SSE
        event.node.res.write(`data: ${JSON.stringify({ chunk, isComplete, result })}\n\n`);
        
        if (isComplete) {
          break;
        }
      }
    } else {
      // Fallback to regular message if streaming not supported
      const response = await chat.sendMessage(message);
      event.node.res.write(`data: ${JSON.stringify({ chunk: response.message, isComplete: false })}\n\n`);
      event.node.res.write(`data: ${JSON.stringify({ chunk: '', isComplete: true, result: response })}\n\n`);
    }
    
    // End the stream
    event.node.res.end();
    return;
  }

  throw createError({
    statusCode: 400,
    statusMessage: `Invalid endpoint: ${endpoint}`,
  });
});