import { NvidiaNimSource } from "pickvocab-dictionary";

export default defineEventHandler(async (event) => {
  // Handle CORS
  setHeader(event, 'Access-Control-Allow-Origin', '*');
  setHeader(event, 'Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  setHeader(event, 'Access-Control-Allow-Headers', 'Content-Type, Authorization');
  setHeader(event, 'Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests (OPTIONS)
  if (event.node.req.method === 'OPTIONS') {
    event.node.res.statusCode = 204;
    event.node.res.end();
    return;
  }

  const body = await readBody(event);
  const { apiKey, modelId, modelName, maxToken, endpoint, args, isThinkingModel } = body;

  const source = new NvidiaNimSource({
    modelName,
    modelId,
    maxToken,
    apiKey,
    isThinkingModel,
  });

  // Set up Server-Sent Events
  setHeader(event, 'Content-Type', 'text/event-stream');
  setHeader(event, 'Cache-Control', 'no-cache');
  setHeader(event, 'Connection', 'keep-alive');

  // Handle different streaming endpoints
  if (endpoint === 'getMeaningInContextShortStream') {
    const [word, context, offset] = args;
    
    for await (const { chunk, isComplete, result } of source.getMeaningInContextShortStream(word, context, offset)) {
      // Send chunk as SSE
      event.node.res.write(`data: ${JSON.stringify({ chunk, isComplete, result })}\n\n`);
      
      if (isComplete) {
        break;
      }
    }
    
  } else if (endpoint === 'getMeaningInContextStream') {
    const [word, context, offset] = args;
    
    for await (const { chunk, isComplete, result } of source.getMeaningInContextStream(word, context, offset)) {
      // Send chunk as SSE
      event.node.res.write(`data: ${JSON.stringify({ chunk, isComplete, result })}\n\n`);
      
      if (isComplete) {
        break;
      }
    }
    
  } else if (endpoint === 'getMeaningInContextShortForLanguageStream') {
    const [word, context, offset, language] = args;
    
    for await (const { chunk, isComplete, result } of source.getMeaningInContextShortForLanguageStream(word, context, offset, language)) {
      // Send chunk as SSE
      event.node.res.write(`data: ${JSON.stringify({ chunk, isComplete, result })}\n\n`);
      
      if (isComplete) {
        break;
      }
    }
    
  } else {
    event.node.res.statusCode = 400;
    event.node.res.write(`data: ${JSON.stringify({ error: `Invalid streaming endpoint: ${endpoint}` })}\n\n`);
  }

  // End the stream
  event.node.res.end();
});