import { Dictionary } from 'pickvocab-dictionary';
import { useEventBus } from '@vueuse/core';
import type { Ref } from 'vue';
import type { WordInContextEntry } from 'pickvocab-dictionary';
import type { LookupEvent } from './events';
import { reduceContext } from '~/utils/contextCard';
import { nextSimpleRequestId, currentSimpleRequestId } from './requestIds';

// ------------------------------
// Helper functions
// ------------------------------

async function performEnglishStream(
  dictionary: Dictionary,
  reduced: { selectedText: string; text: string; offset: number },
  bus: ReturnType<typeof useEventBus<LookupEvent>>,
  currentRequestId: number,
): Promise<boolean> {
  try {
    const generator = dictionary.getMeaningInContextShortStream(
      reduced.selectedText,
      reduced.text,
      reduced.offset,
    );

    for await (const chunk of generator) {
      if (currentSimpleRequestId() !== currentRequestId) {
        return true; // cancelled, not error
      }

      if (chunk.isComplete) {
        bus.emit({
          type: 'StreamCompleted',
          streamType: 'simple',
          language: 'English',
          requestId: currentRequestId,
        });
      } else {
        bus.emit({
          type: 'SimpleTextChunk',
          language: 'English',
          chunk: chunk.chunk,
          requestId: currentRequestId,
        });
      }
    }
    return true;
  } catch (err) {
    console.error('[simple-stream] Streaming failed', err);
    return false; // trigger fallback
  }
}

async function performEnglishNonStreaming(
  dictionary: Dictionary,
  reduced: { selectedText: string; text: string; offset: number },
  bus: ReturnType<typeof useEventBus<LookupEvent>>,
  currentRequestId: number,
  wordEntryRef: Ref<WordInContextEntry | undefined>,
) {
  try {
    const result = await dictionary.getMeaningInContextShort(
      reduced.selectedText,
      reduced.text,
      reduced.offset,
    );

    if (wordEntryRef.value) {
      wordEntryRef.value.definitionShort = result.definitionShort;
    }

    bus.emit({
      type: 'StreamCompleted',
      streamType: 'simple',
      language: 'English',
      requestId: currentRequestId,
    });
  } catch (nonStreamError: any) {
    bus.emit({
      type: 'StreamError',
      streamType: 'simple',
      language: 'English',
      error: nonStreamError.message,
      requestId: currentRequestId,
    });
  }
}

interface LookupParams {
  word: string;
  context: string;
  offset: number;
  dictionary: Dictionary;
  bus: ReturnType<typeof useEventBus<LookupEvent>>;
  wordEntryRef: Ref<WordInContextEntry | undefined>;
}

/**
 * Streams a simple English definition for the provided word in context.
 * Emits events so that the state layer can update reactively.
 */
export async function lookupSimpleEnglish({
  word,
  context,
  offset,
  dictionary,
  bus,
  wordEntryRef,
}: LookupParams): Promise<void> {
  const currentRequestId = nextSimpleRequestId();
  const reduced = reduceContext(context, word, offset, 3);

  // Notify listeners that a new stream has started.
  bus.emit({
    type: 'StreamStarted',
    streamType: 'simple',
    language: 'English',
    requestDetails: {
      word: reduced.selectedText,
      context: reduced.text,
      offset: reduced.offset,
    },
  });

  const streamed = await performEnglishStream(dictionary, reduced, bus, currentRequestId);

  if (!streamed) {
    await performEnglishNonStreaming(
      dictionary,
      reduced,
      bus,
      currentRequestId,
      wordEntryRef,
    );
  }
} 