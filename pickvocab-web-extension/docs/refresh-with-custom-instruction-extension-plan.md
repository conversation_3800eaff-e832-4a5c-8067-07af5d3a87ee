# Refresh with Custom Instruction - Web Extension Implementation Plan
## ✨ UPDATED: Sub-View Approach (Better than Client's Nested Dialog)

## Introduction

The Pickvocab web extension currently provides basic text revision functionality through a simple refresh button. Users can revise their text, but they have no way to provide specific instructions for how they want their text to be modified. This limitation forces users to manually edit the output or repeatedly refresh until they get the desired result.

The client application has successfully implemented a comprehensive custom instruction feature that allows users to specify exactly how they want their text revised (e.g., "make it shorter", "use simpler language", "add specific examples"). This feature has proven valuable for giving users precise control over their revisions while maintaining the AI's writing assistance capabilities.

## Goal

**Primary Objective**: Implement the custom instruction refresh feature in the web extension to achieve feature parity with the client application, while leveraging the extension's unique sub-view architecture for an improved user experience.

**Success Criteria**:
1. **Feature Parity**: All core functionality from the client implementation adapted for extension context
2. **Enhanced UX**: Utilize extension's sub-view system for better space utilization than client's nested dialogs
3. **Zero Breaking Changes**: Preserve all existing refresh functionality as default behavior
4. **Architectural Consistency**: Follow extension's established patterns and conventions
5. **Performance**: Maintain streaming capabilities and responsiveness

**User Benefits**:
- **Precise Control**: Users can specify exactly how they want their text revised
- **Efficiency**: Reduce trial-and-error revision cycles
- **Learning**: Example instructions help users understand revision possibilities
- **Consistency**: Same powerful feature available across web and extension platforms

## Overview
Implement the custom instruction refresh feature in the web extension, **IMPROVING** on the client implementation by using the extension's existing sub-view navigation system instead of nested dialogs.

## Current State Analysis

### ✅ Existing Infrastructure
- **Refresh functionality**: Working in `useWritingRevisionHandler.ts` (line 569)
- **Streaming architecture**: Already supports real-time revision streaming
- **UI components**: Shadcn-vue dialog components available
- **Prompt templates**: `refreshPrompts.ts` with vocabulary and basic refresh prompts
- **Event flow**: `WritingAssistantView.vue` → `WritingOutputSection.vue` → `OutputSectionHeader.vue`

## Architecture Decision: Sub-View vs Dialog

### ❌ Client Approach (Nested Dialog)
- CustomInstructionDialog.vue opens as modal within writing interface
- Works for full-page client application
- Limited space and awkward UX in extension popup context

### ✅ Extension Approach (Sub-View Navigation) 
- CustomInstructionView.vue as full sub-view (like ManageCustomTonesView)
- Leverages existing `writingAssistantSubView` system
- Full 600px width available, better UX
- Consistent navigation patterns

### 🎯 Implementation Required
1. **Sub-View Infrastructure**: Add 'custom_instruction' to existing sub-view system
2. **CustomInstructionView**: Full-page component for user input
3. **Enhanced Handler**: Accept optional custom instruction parameter  
4. **Custom Prompts**: Dedicated templates for custom instruction handling
5. **State Management**: Recent instructions persistence and validation

## Current Extension Sub-View System

**Existing Implementation:**
```typescript
// writingStore.ts (line 75-78)
const writingAssistantSubView = ref<'main' | 'manage_tones'>('main');
function setWritingAssistantSubView(view: 'main' | 'manage_tones') {
  writingAssistantSubView.value = view;
}
```

**Conditional Rendering Pattern:**
```vue
<!-- FloatingPopupContainer.vue (lines 23-24) -->
<WritingAssistantView v-if="writingAssistantSubView === 'main'" />
<ManageCustomTonesView v-else-if="writingAssistantSubView === 'manage_tones'" />
```

## Implementation Plan

### Phase 1: Sub-View Infrastructure (Priority: High)

#### 1.1 Update Store Type Definition
**File**: `components/write/stores/writingStore.ts`
```typescript
// Change line 75:
const writingAssistantSubView = ref<'main' | 'manage_tones' | 'custom_instruction'>('main');

// Update function signature line 76:
function setWritingAssistantSubView(view: 'main' | 'manage_tones' | 'custom_instruction') {
```

#### 1.2 Add Sub-View to Container
**File**: `entrypoints/content/FloatingPopupContainer.vue`
```vue
<!-- Add after line 24 -->
<CustomInstructionView v-else-if="writingAssistantSubView === 'custom_instruction'" />
```

#### 1.3 Create CustomInstructionView.vue
**New file**: `components/write/CustomInstructionView.vue`
- **Header**: Gradient emerald background with icon and title (matching client's elegant design)
- **Content**: Large textarea (full 600px width) with focus styling and transitions
- **Examples**: Button grid with 8 diverse examples (not just "make it more..." patterns)
- **Context**: Clear indication showing "Revision X" being modified with proper indexing
- **Actions**: Apply/Cancel with keyboard shortcuts (Ctrl/Cmd+Enter)
- **Input Clearing**: Clear previous instruction when dialog reopens
- **Validation**: Real-time validation without character limits

### Phase 2: Navigation Integration (Priority: High)

#### 2.1 Modify OutputSectionHeader.vue
**Current behavior**: Single refresh button
**New behavior**: Split button with dropdown
```vue
<!-- Main button: preserves existing refresh behavior -->
<button @click="$emit('refreshRevision')" ...>

<!-- Dropdown: navigates to custom instruction sub-view -->
<DropdownMenuItem @click="$emit('openCustomInstruction')">
  Refresh with instruction...
</DropdownMenuItem>
```

#### 2.2 Update Event Flow
**WritingOutputSection.vue**: Pass through `openCustomInstruction` event
**WritingAssistantView.vue**: Handle event with `writingStore.setWritingAssistantSubView('custom_instruction')`

### Phase 3: Backend Integration (Priority: High)

#### 3.1 Enhanced Revision Handler
**File**: `composables/useWritingRevisionHandler.ts`
```typescript
// Modify refreshRevision function (line 569)
async function refreshRevision(customInstruction?: string) {
  if (customInstruction) {
    // Route to custom instruction handlers
    if (writingStore.useVocabulary) {
      await refreshVocabularyWithCustomInstruction(customInstruction);
    } else {
      await refreshRevisionWithCustomInstruction(customInstruction);
    }
    return;
  }
  // ... existing logic unchanged
}
```

#### 3.2 Custom Instruction Prompts
**New files**: 
- `components/write/utils/customInstructionRevisionPrompt.ts` - Basic custom refresh
- `components/write/utils/customInstructionVocabularyPrompt.ts` - Vocabulary-enabled custom refresh

**High-Level Prompt Architecture**:

**A. customInstructionRevisionPrompt()**
```typescript
function customInstructionRevisionPrompt(
  selectedToneStyleDescription: string,    // Current tone (e.g., "Professional", "Casual")
  userText: string,                       // Original user input text
  currentRevision: string,                // The revision being modified
  customInstruction: string,              // User's specific instruction
  revisionNumber: number = 1              // Target revision number for output
): string
```

**B. customInstructionVocabularyPrompt()**
```typescript
function customInstructionVocabularyPrompt(
  selectedToneStyleDescription: string,    // Current tone setting
  userText: string,                       // Original user input text  
  currentRevision: string,                // The revision being modified
  customInstruction: string,              // User's specific instruction
  userVocabularies: FormattedVocabulary[], // User's vocabulary words with IDs
  revisionNumber: number = 1              // Target revision number for output
): string
```

### Phase 4: State & UX (Priority: Medium)

#### 4.1 Context Passing
- Pass current revision data to CustomInstructionView
- Show revision number and preview in context section

#### 4.2 Input State Management
- Clear instruction input when view opens
- Real-time validation without character limits
- Keyboard shortcuts for quick application

## Technical Advantages of Sub-View Approach

### 🎯 Better UX
- **Full Width**: 600px available vs cramped nested dialog
- **Consistent Navigation**: Matches existing manage_tones pattern
- **No Modal Stacking**: Clean single-level interface
- **More Space**: Better layout for examples and instruction input
- **Elegant Design**: Gradient headers and smooth transitions

### 🏗️ Better Architecture  
- **Existing Infrastructure**: Leverages proven sub-view system
- **State Management**: Consistent with extension patterns
- **Navigation**: Back button already established
- **Performance**: No modal management overhead

### 🔧 Development Benefits
- **Proven Pattern**: ManageCustomTonesView as reference
- **Less Complexity**: No dialog state management
- **Easier Testing**: Standard component testing
- **Maintainability**: Follows extension conventions

## File Changes Summary

### New Files
```
components/write/
├── CustomInstructionView.vue           [NEW - Main sub-view with gradient design]
└── utils/
    ├── customInstructionVocabularyPrompt.ts  [NEW - Vocabulary-enabled prompts]
    └── customInstructionRevisionPrompt.ts    [NEW - Basic revision prompts]
```

### Modified Files
```
components/write/
├── stores/writingStore.ts              [Type update]
├── components/
│   ├── OutputSectionHeader.vue         [Split button]
│   └── WritingOutputSection.vue        [Event passing]
├── composables/
│   └── useWritingRevisionHandler.ts    [Enhanced handler]
└── WritingAssistantView.vue            [Navigation handling]

entrypoints/content/
└── FloatingPopupContainer.vue          [Sub-view rendering]
```

## Success Metrics

✅ **Zero Breaking Changes**: Existing refresh functionality unchanged  
✅ **Better UX**: Full width utilization vs nested dialog constraints  
✅ **Consistent Navigation**: Matches manage_tones pattern  
✅ **Feature Parity**: All client functionality adapted  
✅ **Performance**: Leverages existing streaming architecture  
✅ **Maintainability**: Follows extension architectural patterns  

## Development Timeline
- **Phase 1**: 2 hours (Sub-view infrastructure)
- **Phase 2**: 2 hours (Navigation integration)  
- **Phase 3**: 3 hours (Backend integration)
- **Phase 4**: 1 hour (Polish)
- **Total**: 8 hours

This implementation **improves upon** the client's nested dialog approach by leveraging the extension's superior sub-view architecture for better UX and maintainability.

## Detailed Component Analysis

### Current Client Implementation (Reference)
Based on the client plan document, the feature has been successfully implemented with:

1. **CustomInstructionDialog.vue**: Complete modal with gradient design, examples, recent instructions
2. **Split button**: Main refresh + dropdown with custom option
3. **Dedicated prompts**: `customInstructionVocabularyPrompt.ts` and `customInstructionRevisionPrompt.ts`
4. **Smart context**: Shows which revision will be modified
5. **Parser compatibility**: Uses standard "# Revision X" format

### Extension-Specific Adaptations Needed

#### UI Constraints
- **Width**: Extension popup limited to 600px (vs full browser window)
- **Height**: Must fit within extension popup viewport
- **Components**: Use Shadcn-vue (matches existing extension UI)

#### Architecture Differences
- **Streaming-first**: Extension already uses streaming (client retrofit)
- **Event system**: Extension uses webext-bridge messaging
- **State management**: Extension has different store structure

#### Functional Parity Goals
- All client features adapted to extension context (except recent instructions)
- Same UX patterns and user flows with gradient design
- Compatible instruction format and examples
- Proper revision context display with indexing
- Keyboard shortcuts and input clearing behavior

### Implementation Notes

#### Phase 1 Details: OutputSectionHeader.vue Split Button
Based on client implementation, create split button design:
- **Main button**: Preserves existing refresh behavior with rounded-left styling
- **Dropdown trigger**: Adjacent button with rounded-right styling and chevron icon
- **Visual integration**: Seamless border connection between buttons
- **DropdownMenu**: Use Shadcn-vue DropdownMenu component
- **Custom option**: "Refresh with instruction" with wand icon
- **Event flow**: Emit custom instruction events to parent components

Key design elements from client:
- Clean visual separation between main and dropdown buttons
- Consistent hover states and disabled styling
- Proper ARIA labels for accessibility

#### Phase 2 Details: useWritingRevisionHandler.ts Enhancement
Current `refreshRevision()` function (line 569):
```typescript
async function refreshRevision() {
  if (isRevising.value || isStreamingRevision.value) return;
  // ... existing logic
}
```

Enhanced version:
```typescript
async function refreshRevision(customInstruction?: string) {
  if (isRevising.value || isStreamingRevision.value) return;
  
  if (customInstruction) {
    // Route to custom instruction handlers
    if (writingStore.useVocabulary) {
      await refreshVocabularyWithCustomInstruction(customInstruction);
    } else {
      await refreshRevisionWithCustomInstruction(customInstruction);
    }
    return;
  }
  
  // ... existing logic unchanged
}
```

This approach ensures zero breaking changes while adding the new functionality.

## Key Missing Points Added from Client Implementation

### 1. **Elegant UI Design** ✨
- **Gradient header**: Emerald gradient background with proper icon placement
- **Enhanced styling**: Focus ring effects, smooth transitions, shadow treatments
- **Professional appearance**: Matches client's polished modal design

### 2. **Enhanced User Experience** 🎯
- **Input clearing**: Automatically clear instruction when reopening (prevents confusion)
- **Character validation**: Real-time validation without arbitrary limits
- **Loading states**: Proper feedback during instruction application
- **Keyboard shortcuts**: Ctrl/Cmd+Enter for quick application

### 3. **Diverse Example Instructions** 📝
Based on client implementation, include varied examples:
- "Make it shorter"
- "Make it more persuasive" 
- "Use simpler language"
- "Add specific examples"
- "Don't use the word '...'"
- "Use the word '...'"
- "Remove passive voice"
- "Add transition sentences"

### 4. **Proper Revision Context** 📍
- Display "Revision X" being modified with accurate indexing
- Pass `currentRevisionIndex` and `totalRevisions` props
- Clear user understanding of which text will be modified

### 5. **Dedicated Prompt Templates** 🔧
- **Separate prompts**: Don't modify existing refresh prompts
- **Parser compatibility**: Output "# Revision X" format
- **Instruction integration**: Proper custom instruction handling
- **Vocabulary support**: Maintain vocabulary integration when enabled

### 6. **Architecture Consistency** 🏗️
- **Event naming**: Use consistent event patterns
- **Component integration**: Proper parent-child communication
- **State management**: Clean instruction passing and handling

## Implementation Priority Updates

### Critical Must-Haves (from client analysis):
1. ✅ Split button with dropdown (OutputSectionHeader pattern)
2. ✅ Gradient header design with emerald theme
3. ✅ Custom instruction prompts (separate from existing)
4. ✅ Input clearing behavior
5. ✅ Revision context display
6. ✅ Keyboard shortcuts (Ctrl/Cmd+Enter)

### Extension-Specific Adaptations:
1. 🚫 **Skip recent instructions** (per user request)
2. ✅ Use sub-view instead of modal dialog
3. ✅ Leverage existing navigation patterns
4. ✅ Maintain 600px width advantage

This updated plan now includes all the polished features from the client implementation while adapting them appropriately for the extension's sub-view architecture.