# Migration Plan: useDictionaryLookup2 & DefinitionPopup2 for Web-Extension

> This document explains how to port the new concurrent-stream lookup architecture (already live in the client app) into the browser extension.

---

## 1. Directory Layout

```
pickvocab-web-extension/entrypoints/content/lookup2/
 ├─ events.ts
 ├─ requestIds.ts
 ├─ lookupState.ts
 ├─ simpleEnglishStream.ts
 ├─ simpleLanguageStream.ts
 ├─ detailedStream.ts
 ├─ idManager.ts          # extension-specific variant
 └─ useDictionaryLookup2.ts
```

* Copy each file from
  `pickvocab-client/components/app/contextualMeaning/ContextView2/` and adjust imports (see §2).

## 2. Import & Alias Tweaks

| Client Import | Extension Replacement |
|---------------|----------------------|
| `~/stores/llm` | `../popup/stores/llm` |
| `RemoteWordInContextApi` REST calls | `sendMessage()` bridge calls |
| `@/` / `~/` aliases | relative paths or add Vite alias |
| `vue-router` usage | **remove** (no router in content-script) |

## 3. `idManager.ts` (extension version)

```ts
// idManager.ts – excerpt
import { sendMessage } from 'webext-bridge/content-script';
…
if (hasRealId) {
  await sendMessage('wordInContext:put', { word: entry }, 'background');
} else {
  const created = await sendMessage('wordInContext:create', { base }, 'background');
  wordEntry.value = created as WordInContextEntry;
  // no router.replace – URL stays unchanged in content-script
}
```

## 4. Build `useDictionaryLookup2.ts`

Replicate client version; only change the imports plus the `idManager` reference.

## 5. Author `DefinitionPopup2.vue`

Start from `LookupModal2.vue` and:

1. Replace the Dialog shell with existing popup markup.
2. Import the new composable:
   ```ts
   const {
     wordEntry,
     isLoading,
     isStreamingSimple,
     isStreamingDetailed,
     errorMessage,
     activeSimpleLanguages,
     lookupSimpleEnglish,
     lookupSimpleLanguage,
     lookupDetailed,
   } = useDictionaryLookup2();
   ```
3. Retain `isDetailed`, `selectedSimpleViewLanguage`, `isStreamingSimpleCurrentLang`, and `refresh()` helpers.
4. Keep add-card logic that already uses bridge messages.

## 6. Wire Up `utils.ts`

```diff
-import Popup from "./DefinitionPopup.vue";
+import Popup from "./DefinitionPopup2.vue";
```

## 7. Manual Test Matrix

| Scenario | Expected Behaviour |
|----------|--------------------|
| Simple English stream | Progressive text, no errors |
| Toggle Detailed mid-stream | Both streams run concurrently |
| Switch languages rapidly | Each language keeps its own content |
| Refresh current view | Only that stream restarts |
| Save card | Background `contextCard:create` succeeds |

## 8. Important Fixes Applied

### ID Manager Singleton Pattern Issue
**Problem:** The second word lookup couldn't be saved because the ID manager used a global singleton pattern that prevented new component instances from registering their own listeners. The temp ID was never converted to a real ID from the server.

**Solution:** Removed the global `listenerInitialized` flag that was preventing new instances from registering listeners. Initially added a WeakMap to track refs, but this became unnecessary once proper Vue app lifecycle management was implemented (see next issue).

### Vue App Lifecycle Management Issue  
**Problem:** When creating multiple popups in succession, the old Vue app instances were not properly cleaned up, causing duplicate event listeners and multiple ID manager instances to process the same events. This resulted in duplicate entries being created on the server.

**Solution:** Store a reference to the current Vue app and properly unmount it before creating a new popup:

```typescript
// In utils.ts
let currentFloatingPopupApp: App | null = null;

export async function createFloatingPopup(...) {
  // Unmount existing Vue app if any
  if (currentFloatingPopupApp) {
    currentFloatingPopupApp.unmount();
    currentFloatingPopupApp = null;
  }
  
  // ... create new popup
  const popupApp = createApp(FloatingPopupContainer);
  currentFloatingPopupApp = popupApp; // Store reference
  
  // Also unmount on close
  const closePopup = (event: MouseEvent) => {
    if (currentFloatingPopupApp) {
      currentFloatingPopupApp.unmount();
      currentFloatingPopupApp = null;
    }
    // ... rest of cleanup
  };
}
```

This ensures proper cleanup of all Vue components and their event listeners via `onScopeDispose` hooks.

**Final Simplification:** After implementing proper Vue app lifecycle management, the `managedRefs` WeakMap in `idManager.ts` became redundant since each popup now gets completely new component instances with new refs. The WeakMap was removed, simplifying the code while maintaining the same functionality.

## 9. Checklist

- [x] `lookup2/` folder committed
- [x] All imports compile in extension build
- [x] Popup switch in `utils.ts`
- [x] Manual QA passes matrix above
- [x] Vue app lifecycle properly managed to prevent duplicate instances

---

**Outcome:** Web-extension gains the same robust, race-free lookup experience as the client with minimal duplication and proper cleanup of resources. 