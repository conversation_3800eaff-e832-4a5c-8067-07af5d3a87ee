# Web Extension Streaming and Refresh Enhancement Plan

## Overview
The web extension has a comprehensive writing assistant but lacks two key features from the client:
1. **Streaming Support** - Real-time content generation
2. **Varied Refresh Prompts** - Different approaches on refresh

## 🚀 **Incremental Implementation Strategy**

**This plan is divided into 4 incremental phases, each building on the previous and providing verifiable value:**

1. **Phase 1 (MVP)**: Grammar Check Streaming Only (3-4 hours)
2. **Phase 2**: Basic Revise Streaming (2-3 hours) 
3. **Phase 3**: Vocabulary Integration Streaming (3-4 hours)
4. **Phase 4**: Refresh Functionality (2-3 hours)

**✅ COMPLETED: Phase 1 - Grammar Check Streaming MVP**
**✅ COMPLETED: Phase 2 - Basic Revise Streaming**
**✅ COMPLETED: Phase 3 - Vocabulary Integration Streaming**
**✅ COMPLETED: Phase 4 - Refresh Functionality**

## 📋 **Recent Client Changes to Port**

### **Reduced Revision Count: 3 → 1**
The client has been updated to generate **only 1 revision** instead of 3 for improved streaming performance:

- **`reviseTextSimpleMarkdownPrompt.ts`**: Now outputs `# Revision 1` only
- **`useMyVocabularyMarkdownPrompt.ts`**: Now outputs `# Revision 1` only  
- **Grammar check**: Already outputs only 1 revision

**Impact**: Simplifies parsing, reduces complexity, faster streaming experience.

**Extension Status**: ✅ Extension already expects 1 revision for grammar check, needs updates for basic/vocabulary revise.

## Current State Analysis

### Extension Architecture ✅
- **Writing Handler**: `useWritingRevisionHandler.ts` manages revision flow
- **API Layer**: `useWritingRevisionApi.ts` handles LLM calls via background script
- **Background Script**: `revisionHandler.ts` calls LLM providers
- **UI Components**: `WritingOutputSection.vue`, `RevisedTextViewer.vue`, `LLMFeedbackDisplay.vue`
- **Basic Refresh**: Currently calls `initiateRevision()` again (same prompt)

### Available Streaming Infrastructure ✅
- **ChatSource Interface**: All providers support `sendMessageStream()` 
- **AsyncGenerator Pattern**: `{ chunk: string; isComplete: boolean; result?: ChatResponse }`
- **Parser**: `parseMarkdownRevisions()` exists, need `parsePartialMarkdownRevisions()`

### ❌ **Critical Architecture Difference**
**Web Extension Limitation**: Cannot use AsyncGenerator pattern directly due to message passing constraints.
**Solution**: Implement message-based streaming simulation with progressive updates.

## 📚 **IMPORTANT: Study Client Implementation First**

Before implementing each phase, carefully study the corresponding client files:

### **Essential Client Files to Reference**

#### Core Streaming Implementation
- **`/pickvocab-client/components/app/write/useRevisionApi.ts`**
  - Study `generateRevisionStream()` AsyncGenerator pattern
  - Understand fallback logic for non-streaming providers
  - Copy error handling patterns

- **`/pickvocab-client/components/app/write/useWriteRevisionHandler.ts`**
  - Study `callLLMAndParseResponseStream()` method (lines ~300-400)
  - Understand streaming state management pattern
  - Copy `appendToExisting` logic for refresh functionality
  - Study vocabulary vs non-vocabulary flow handling

#### Refresh Implementation
- **`/pickvocab-client/components/app/write/refreshRevisionPrompt.ts`**
  - Copy entire function - exact prompt logic needed
  - Understand how previous revisions are passed to LLM

- **`/pickvocab-client/components/app/write/refreshVocabularyPrompt.ts`**
  - Copy entire function - exact prompt logic needed
  - Understand vocabulary integration in refresh prompts

#### UI Streaming Patterns
- **`/pickvocab-client/components/app/write/RevisedTextViewer.vue`**
  - Study streaming props: `revision?: Partial<RevisionData>`, `isStreaming?: boolean`
  - Copy progressive display animations
  - Understand field-level undefined vs progressive content handling

- **`/pickvocab-client/components/app/write/OutputSection.vue`**
  - Study how streaming state is coordinated across child components
  - Understand progressive section visibility logic

#### Parsing Logic
- **`/pickvocab-client/components/app/write/markdownRevisionParser.ts`**
  - Copy `parsePartialMarkdownRevisions()` function exactly
  - Understand error handling for incomplete markdown

### **Key Implementation Patterns to Follow**

1. **Streaming State Pattern**: `isStreamingRevision`, `isLoadingVocabularyCards` 
2. **Refresh Logic**: Append new revisions, switch to newest, pass all previous revisions to LLM
3. **Placeholder Pattern**: Add empty revision immediately on refresh for UI consistency
4. **Progressive Parsing**: Use partial parser for chunks, full parser for final result
5. **Error Fallback**: Graceful degradation to non-streaming mode

### **Architecture Adaptation Notes**

- **AsyncGenerator → Message Passing**: Convert client's generator pattern to message-based simulation
- **Direct API Calls → Background Script**: Route all LLM calls through background script
- **Immediate State Updates → Accumulated Chunks**: Accumulate message chunks before UI updates

# ✅ Phase 1: Grammar Check Streaming MVP - COMPLETED

**Goal**: Prove message-passing streaming architecture works with the simplest possible flow.

**Status**: ✅ **COMPLETED** - Grammar check now streams in real-time with beautiful animations

## ✅ Phase 1 Implementation Completed

### ✅ 1.1 Core Streaming Infrastructure
- **File**: `components/write/utils/markdownRevisionParser.ts`
- **✅ Added**: `parsePartialMarkdownRevisions()` function (copied from client)
- **✅ Tested**: Partial parsing works with incomplete markdown

### ✅ 1.2 Background Script Streaming Handler
- **File**: `entrypoints/background.ts`
- **✅ Added**: `writingAssistantGenerateRevisionStream` message handler
- **✅ Fixed**: Missing `storage` and `getAxiosInstance` imports that were preventing handler registration

### ✅ 1.3 Background Revision Handler
- **File**: `entrypoints/background/revisionHandler.ts`
- **✅ Added**: `handleGenerateRevisionStream()` function
- **✅ Implemented**: Progressive updates via message passing
- **✅ Added**: Fallback to non-streaming for unsupported providers

### ✅ 1.4 Streaming State Management
- **File**: `useWritingRevisionHandler.ts`
- **✅ Added**: `isStreamingRevision` and `currentStreamId` refs
- **✅ Added**: Message listener for `writingAssistantStreamChunk`
- **✅ Updated**: `initiateGrammarCheck()` to use streaming
- **✅ Fixed**: Race condition by setting stream ID before sending request

### ✅ 1.5 UI Components for Streaming
- **File**: `components/write/components/RevisedTextViewer.vue`
- **✅ Added**: `isStreaming` prop and `Partial<RevisionData>` support
- **✅ Implemented**: Progressive content display

- **File**: `components/write/components/WritingOutputSection.vue`
- **✅ Added**: Streaming state props and ShinyText generating indicator
- **✅ Updated**: Condition to show output section during streaming

- **File**: `components/ui/ShinyText.vue` (NEW)
- **✅ Created**: Beautiful shiny text animation component copied from client

### ✅ 1.6 API Layer Streaming
- **File**: `components/write/api/index.ts`
- **✅ Added**: `generateRevisionStream()` and `generateRevisionStreamWithId()`
- **✅ Implemented**: Message-based streaming initiation

## ✅ Phase 1 Success Criteria - ALL MET

✅ **Grammar Check shows real-time streaming text**
✅ **Text appears progressively word-by-word**  
✅ **Beautiful "Generating response" animation with shiny text and pulsing dots**
✅ **Fallback to non-streaming works on errors**
✅ **No breaking changes to existing functionality**
✅ **Race condition fixed - streaming works reliably**
✅ **Clean console output (all debug logs removed)**

## ✅ Phase 1 Final Implementation

**Grammar check now provides**:
- 🌟 **Shiny "Generating response" text** that shimmers as it generates
- 💫 **Three pulsing dots** with staggered animation timing  
- 📝 **Real-time progressive text** appearing as the LLM generates
- 🔄 **Smooth transitions** between loading, streaming, and completed states
- 🎯 **Perfect UX** matching the client application exactly

---

# ✅ Phase 2: Basic Revise Streaming (Non-Vocabulary Flow) - COMPLETED

**Goal**: Extend streaming to the basic revise flow (no vocabulary integration).

**Status**: ✅ **COMPLETED** - Basic revise now streams in real-time with updated 1-revision format

## ✅ Phase 2 Implementation Completed

### ✅ 2.1 Update Basic Revise Prompts
- **File**: `components/write/utils/generateRevisionMarkdownPrompt.ts`
- **✅ Updated**: `generateRevisionPrompt()` to match client's **1 revision** format
- **✅ Reference**: Copied exact prompt from client `/pickvocab-client/components/app/write/reviseTextSimpleMarkdownPrompt.ts`
- **✅ Tested**: Prompt now generates `# Revision 1` format instead of 3 revisions

### ✅ 2.2 Extend Streaming to Basic Revise
- **File**: `useWritingRevisionHandler.ts`
- **✅ Added**: `initiateRevisionDirectlyStream()` method following grammar check pattern
- **✅ Updated**: `initiateRevisionDirectly()` to use streaming by default
- **✅ Logic**: Same streaming pattern as grammar check but with basic revise prompt
- **✅ Added**: `currentStreamType` tracking for proper history saving
- **✅ Fixed**: TypeScript errors with trigger type validation

### ✅ 2.3 Streaming State Management
- **File**: `useWritingRevisionHandler.ts`
- **✅ Added**: `currentStreamType` ref for tracking revision type
- **✅ Updated**: Message listener to handle different trigger types correctly
- **✅ Fixed**: Proper cleanup of streaming state on completion/error

### ✅ 2.4 Testing with Different Content Types
- **✅ Verified**: Basic revise triggers streaming correctly
- **✅ Tested**: Works with various content lengths
- **✅ Confirmed**: Smooth streaming experience maintained

## ✅ Phase 2 Success Criteria - ALL MET

✅ **Basic revise (non-vocabulary) shows real-time streaming**
✅ **Works with content of various lengths**  
✅ **Uses updated 1-revision prompt format**
✅ **Maintains smooth UX for longer generation times**
✅ **Fallback works correctly**
✅ **Proper trigger type tracking for history saving**
✅ **Clean TypeScript implementation without errors**

## ✅ Phase 2 Final Implementation

**Basic revise now provides**:
- 🌟 **Real-time streaming** for all basic revision requests
- 📝 **1-revision format** matching the client application
- 🎯 **Perfect UX consistency** with grammar check streaming
- 📊 **Proper history tracking** with correct trigger types
- 🔄 **Smooth transitions** between loading, streaming, and completed states

---

# ✅ Phase 3: Vocabulary Integration Streaming - COMPLETED

**Goal**: Add streaming to the most complex flow - vocabulary integration with similarity search.

**Status**: ✅ **COMPLETED** - Vocabulary revisions now stream in real-time with proper word highlighting

## ✅ Phase 3 Implementation Completed

### ✅ 3.1 Update Vocabulary Prompts  
- **File**: `components/write/utils/useMyVocabularyMarkdownPrompt.ts`
- **✅ Updated**: `useMyVocabularyPrompt()` to match client's **1 revision** format
- **✅ Reference**: Copied exact prompt from client `/pickvocab-client/components/app/write/useMyVocabularyMarkdownPrompt.ts`
- **✅ Tested**: Prompt now generates `# Revision 1` format with vocabulary IDs

### ✅ 3.2 Add Vocabulary Flow Streaming
- **File**: `useWritingRevisionHandler.ts`
- **✅ Added**: `initiateRevisionWithSearchStream()` method
- **✅ Logic**: Similarity search → streaming → highlighting (in parallel)
- **✅ Updated**: `initiateRevisionWithSearch()` to use streaming by default
- **✅ Coordination**: Proper timing between similarity search and streaming start

### ✅ 3.3 Coordinate Similarity Search + Streaming
- **Challenge**: Similarity search takes time, but streaming should start quickly
- **✅ Solution**: Start streaming immediately after similarity search completes
- **✅ Implementation**: Smooth transition from search to streaming without delays
- **✅ Tested**: Verified smooth transition from search to streaming

### ✅ 3.4 Update Vocabulary Processing for Streaming
- **✅ Critical Fix**: Added `indexToIdMap` parameter to streaming API functions
- **Files Updated**:
  - `components/write/api/index.ts` - Added `indexToIdMap` to streaming functions
  - `components/write/composables/useWritingRevisionApi.ts` - Updated function signatures
  - `entrypoints/background.ts` - Added indexToIdMap to message payload
  - `entrypoints/background/revisionHandler.ts` - Added vocabulary ID processing

### ✅ 3.5 Fix Vocabulary Display Issue
- **✅ Problem**: LLM generated IDs like "2, 3, 9" but UI showed raw indices instead of words
- **✅ Root Cause**: Streaming functions weren't processing vocabulary indices to card IDs
- **✅ Solution**: Added `processLLMRevisions()` call to map indices to `real_card_ids`
- **✅ Result**: Vocabulary words now display correctly in UI and highlighting works

### ✅ 3.6 Complete Vocabulary Flow Testing
- **✅ Tested**: Small vocabulary sets (5-10 words)
- **✅ Tested**: Large vocabulary sets (20+ words) 
- **✅ Tested**: No matching vocabulary words
- **✅ Tested**: Mixed content with some vocabulary matches
- **✅ Verified**: Complete flow works smoothly with streaming

## ✅ Phase 3 Success Criteria - ALL MET

✅ **Vocabulary revise shows real-time streaming**
✅ **Similarity search + streaming coordination works smoothly**
✅ **Vocabulary highlighting applies correctly after streaming**
✅ **Vocabulary cards display properly**
✅ **Uses updated 1-revision prompt format**
✅ **Performance remains good with large vocabulary sets**
✅ **Vocabulary IDs properly map to actual words in UI**
✅ **Background vocabulary highlighting works correctly**

## ✅ Phase 3 Final Implementation

**Vocabulary revisions now provide**:
- 🌟 **Real-time streaming** for all vocabulary-integrated revisions
- 📚 **Proper vocabulary word display** instead of raw indices
- 🎯 **Perfect similarity search coordination** with streaming
- 💡 **Background vocabulary highlighting** after streaming completes
- 📝 **1-revision format** matching the client application
- 🔄 **Smooth transitions** between search, streaming, and highlighting phases

### 📋 **Important Architectural Note: Vocabulary Display Timing**

**Client vs Extension Vocabulary Display Behavior:**

**Client Application:**
- ✅ **Shows vocabulary words DURING streaming** (progressive display)
- **Why**: Direct access to `indexToIdMap` in same JavaScript context
- **Implementation**: Calls `processLLMRevisions(partialRevisions, indexToIdMap)` on every chunk
- **Architecture**: Simple AsyncGenerator pattern with immediate processing

**Web Extension:**
- ✅ **Shows vocabulary words AFTER streaming completes** (final display)
- **Why**: Message passing architecture separates vocabulary processing from UI context
- **Implementation**: `indexToIdMap` lives in background script, vocabulary processing happens once at completion
- **Architecture**: Background script processes vocabulary → sends final result → content script displays

**Technical Reasoning:**
1. **Message Passing Barrier**: Extension's `indexToIdMap` exists in background script context, while streaming chunks arrive in content script context
2. **Separation of Concerns**: Background script handles LLM calls and vocabulary processing, content script handles UI updates
3. **Performance Optimization**: Processing vocabulary once at completion is more efficient than processing on every chunk with message overhead

**Current UX Flow:**
1. Similarity search → Loading spinner
2. Streaming starts → "Generating response..." animation  
3. Content streams → Revision text appears progressively
4. Raw vocabulary indices show → "2, 3, 9" (during streaming)
5. Streaming completes → Vocabulary words appear, highlighting begins

**Alternative Implementation Possible:**
- Could send `indexToIdMap` to content script for progressive vocabulary display
- Would match client behavior but require architectural changes
- Current approach prioritizes **architectural soundness** and **performance** over **progressive vocabulary display**

**Decision**: Maintain current architecture for extension-appropriate separation of concerns while noting the behavioral difference from client.

---


---

# ✅ Phase 4: Refresh Functionality - COMPLETED

**Goal**: Add refresh prompts that generate varied approaches while maintaining tone.

**Status**: ✅ **COMPLETED** - Refresh functionality now works across all flows with proper history management

## ✅ Phase 4 Implementation Completed

### ✅ 4.1 Port Refresh Prompt Functions
- **File**: `components/write/utils/refreshPrompts.ts` (NEW)
- **✅ Added**: `refreshRevisionPrompt()` - Copied from client exactly
- **✅ Added**: `refreshVocabularyPrompt()` - Copied from client exactly  
- **✅ Reference**: Copied entire functions from client refresh prompt files
- **✅ Tested**: Refresh prompts generate different approaches while maintaining tone

### ✅ 4.2 Add Refresh Logic to Revision Handler
- **File**: `useWritingRevisionHandler.ts`
- **✅ Added**: `refreshRevision()` method that appends new revisions
- **✅ Added**: `refreshRevisionWithVocabulary()` and `refreshRevisionBasic()` methods
- **✅ Added**: Track all previous revisions for context (`allPreviousRevisions`)
- **✅ Updated**: `handleRefreshRevision()` to use refresh prompts
- **✅ Implemented**: Proper revision appending with placeholder logic
- **✅ Fixed**: History management - refresh updates existing history entry (not creates new one)
- **✅ Reference**: Followed client's refresh logic with revision appending
- **✅ Tested**: Refresh generates and appends new revisions to same history entry

### ✅ 4.3 Update UI for Multiple Revisions
- **File**: `components/write/components/WritingOutputSection.vue`
- **✅ Verified**: UI already supported multiple revisions with navigation
- **✅ Updated**: `WritingAssistantView.vue` to call correct `refreshRevision()` method
- **✅ Working**: Revision counter and navigation controls
- **✅ Tested**: Users can navigate between all revisions (original + refreshed)

### ✅ 4.4 Critical Architecture Fixes
- **✅ No Separate Trigger Type**: Following client pattern, refresh uses same trigger type as original revision
- **✅ Proper History Management**: Initial revision creates new history, refresh updates existing history
- **✅ Refresh Mode Flag**: Uses `isRefreshMode.value` instead of separate trigger type
- **✅ Streaming Support**: Full streaming support with real-time refresh revision generation
- **✅ Context Passing**: All previous revisions passed to LLM for better variation
- **✅ TypeScript Compliance**: Fixed async/await and unused import issues

## ✅ Phase 4 Success Criteria - ALL MET

✅ **Refresh generates different approaches while maintaining tone**
✅ **New revisions append to existing list (not separate history entries)**
✅ **Users can navigate between all revisions**
✅ **Refresh works for all flows (grammar, basic, vocabulary)**
✅ **Proper history management - refresh updates same history entry**
✅ **Real-time streaming support for refresh revisions**
✅ **Follows client architecture patterns exactly**

## ✅ Phase 4 Final Implementation

**Refresh functionality now provides**:
- 🌟 **Varied refresh prompts** that generate different stylistic approaches
- 📚 **Context-aware generation** using ALL previous revisions for better variation
- 🔄 **Proper history management** - refresh updates existing history entry
- 📝 **Real-time streaming** for refresh revisions with beautiful animations
- 🎯 **Perfect navigation** between original and refreshed revisions
- 🏗️ **Client-compliant architecture** with same trigger types and patterns

### 📋 **Key Technical Implementation Details**

**History Management Pattern**:
```typescript
// Initial revision: create new history entry
if (!isRefreshMode.value) {
  await saveRevisionHistory({ /* complete metadata */ });
} else {
  // Refresh revision: update existing history with all revisions
  await updateHistory(currentHistoryEntryId.value, {
    revisions: allRevisionsResult.value // ALL revisions (original + refreshed)
  });
}
```

**Revision Appending Logic**:
```typescript
// Add placeholder immediately for UI consistency
const placeholderRevision = { /* empty revision */ };
allRevisionsResult.value = [...allRevisionsResult.value, placeholderRevision];
currentRevisionIndex.value = allRevisionsResult.value.length - 1;

// Replace placeholder with actual streamed content
if (isRefreshMode.value) {
  const existingRevisions = allRevisionsResult.value.slice(0, -1);
  allRevisionsResult.value = [...existingRevisions, ...newRevisions];
  currentRevisionIndex.value = existingRevisions.length; // Switch to new revision
}
```

**Context-Aware Prompt Generation**:
```typescript
// Pass ALL previous revisions to LLM for better variation
const allPreviousRevisions = allRevisionsResult.value.map(rev => rev.revision);
const prompt = refreshRevisionPrompt(toneDescription, userText, allPreviousRevisions);
```

## ✅ Phase 4 Complete Success

The web extension now has **complete parity** with the client's refresh functionality, providing users with varied writing approaches while maintaining their selected tone across all flows (grammar check, basic revision, and vocabulary integration).

## ✅ Additional UX Improvements Completed

### ✅ Enhanced Placeholder Text
- **Files**: `components/write/components/RevisedTextViewer.vue`
- **✅ Updated**: Placeholder text from "Content will appear here..." to "Watch your writing transform..."
- **✅ Updated**: Placeholder text from "Revised text will appear here..." to "Your enhanced writing will appear here..."
- **✅ Purpose**: More engaging, user-friendly language appropriate for language learning app

### ✅ Granular Progress Tracking
- **Files**: `components/write/components/WritingOutputSection.vue`, `useWritingRevisionHandler.ts`
- **✅ Added**: `currentStreamingSection` tracking for vocabulary, feedback, and revision phases
- **✅ Implemented**: Dynamic progress messages:
  - "Finding relevant words from your notebooks" - During vocabulary similarity search
  - "Crafting your personalized feedback" - During feedback generation
  - "Creating your learning insights" - During learning focus generation
  - "Enhancing your writing" - During revision generation
- **✅ Purpose**: Users know exactly which phase is running, especially useful for vocabulary flow delays

### ✅ Client Parity for Progress Tracking
- **Files**: `pickvocab-client/components/app/write/useWriteRevisionHandler.ts`, `pickvocab-client/components/app/write/OutputSection.vue`
- **✅ Added**: Same granular progress tracking to client for consistency
- **✅ Fixed**: Missing vocabulary search indicators in client
- **✅ Added**: `currentStreamingSection` state tracking
- **✅ Purpose**: Unified UX across client and extension

### ✅ Refresh Button State Management
- **Files**: Both client and extension `OutputSectionHeader.vue` components
- **✅ Added**: `isRefreshing` prop to disable refresh button during streaming
- **✅ Implemented**: Visual feedback with disabled state and opacity changes
- **✅ Simplified**: Removed spinner animation for cleaner, simpler approach
- **✅ Logic**: Button disabled when `isStreamingRevision || isLoadingVocabularyCards`
- **✅ Purpose**: Prevents multiple refresh clicks and provides clear user feedback

### ✅ Improved Loading Indicators
- **Files**: Extension `WritingOutputSection.vue`
- **✅ Removed**: Redundant loading spinner that appeared alongside streaming indicators
- **✅ Simplified**: Clean transition between loading states
- **✅ Purpose**: Cleaner UI without conflicting loading indicators

## ✅ Complete Implementation Summary

The web extension now provides:
- 🌟 **Complete streaming support** across all revision flows
- 🔄 **Varied refresh functionality** with proper history management
- 📝 **Granular progress tracking** for all phases of generation
- 🎯 **Enhanced UX** with better placeholder text and button states
- 🏗️ **Client parity** with unified experience across platforms
- 🚀 **Production-ready** implementation with comprehensive error handling

## ✅ Final Status: Implementation Complete

All phases completed successfully with additional UX enhancements:
- **Phase 1**: Grammar Check Streaming ✅
- **Phase 2**: Basic Revise Streaming ✅
- **Phase 3**: Vocabulary Integration Streaming ✅
- **Phase 4**: Refresh Functionality ✅
- **Bonus**: Enhanced UX Improvements ✅

The web extension now matches the client's writing assistant capabilities with streaming, refresh, and enhanced user experience.

# Phase 1 Technical Implementation Details

## Message-Based Streaming Flow (Grammar Check Only)

### 1. User Triggers Grammar Check
```typescript
// WritingAssistantView.vue calls existing grammar check logic
writingStore.grammarCheckEnabled = true;
revisionHandler.initiateRevision(); // Existing logic
```

### 2. Grammar Check Streaming Flow  
```typescript
// useWritingRevisionHandler.ts - Updated initiateGrammarCheck()
async function initiateGrammarCheck() {
  isRevising.value = true;
  isStreamingRevision.value = true; // NEW
  
  const streamId = Date.now().toString(); // NEW
  currentStreamId.value = streamId; // NEW
  
  const prompt = grammarCheckPrompt(writingStore.userText);
  
  // NEW: Use streaming API instead of direct call
  await writingApi.generateRevisionStream(prompt, streamId);
}
```

### 3. Background Script Processing
```typescript
// entrypoints/background.ts - NEW handler
onMessage('writingAssistantGenerateRevisionStream', async (message) => {
  const { prompt, streamId } = message.data;
  const tabId = message.sender.tabId;
  
  return await handleGenerateRevisionStream({ prompt, streamId, tabId });
});

// entrypoints/background/revisionHandler.ts - NEW function
export async function handleGenerateRevisionStream({ prompt, streamId, tabId }) {
  // Use existing model selection logic
  const { models, providers } = await loadLlmConfig();
  const modelSource = selectActiveModel(models) || selectPickvocabModel(models);
  
  if (modelSource.sendMessageStream) {
    const generator = modelSource.sendMessageStream(prompt);
    let fullMarkdown = '';
    
    for await (const chunk of generator) {
      if (!chunk.isComplete) {
        fullMarkdown += chunk.chunk;
        
        // Send progressive update to content script
        sendMessage('writingAssistantStreamChunk', {
          streamId,
          chunk: chunk.chunk,
          fullMarkdown,
          isComplete: false
        }, `content-script@${tabId}`);
      } else {
        // Send final result
        const revisions = parseMarkdownRevisions(chunk.result.message);
        sendMessage('writingAssistantStreamChunk', {
          streamId,
          result: revisions,
          isComplete: true
        }, `content-script@${tabId}`);
      }
    }
  }
}
```

### 4. Content Script Progressive Updates
```typescript
// useWritingRevisionHandler.ts - NEW message listener
onMessage('writingAssistantStreamChunk', (message) => {
  if (message.streamId === currentStreamId.value) {
    if (!message.isComplete) {
      // Parse partial markdown and update UI progressively
      const partialRevisions = parsePartialMarkdownRevisions(message.fullMarkdown);
      if (partialRevisions.length > 0) {
        allRevisionsResult.value = partialRevisions;
      }
    } else {
      // Handle final result
      allRevisionsResult.value = message.result;
      isStreamingRevision.value = false;
      currentStreamId.value = null;
      
      // Save history (existing logic)
      await saveRevisionHistory({
        userText: writingStore.userText,
        revisions: message.result,
        triggerType: 'GrammarCheck'
      });
    }
  }
});
```

### 5. UI Progressive Display
```typescript
// RevisedTextViewer.vue - Updated to accept streaming
<script setup lang="ts">
const props = defineProps<{
  revisedText?: string;
  originalTextForCopy?: string;
  revision?: Partial<RevisionData>; // NEW for streaming
  isStreaming?: boolean; // NEW
}>();

const textToDisplay = computed(() => {
  // Support both old props and new streaming props
  if (props.revision?.revision) {
    return props.revision.revision; // Progressive content
  }
  return props.revisedText || '';
});
</script>

<template>
  <div v-if="textToDisplay || isStreaming">
    <!-- Show content progressively -->
    <TiptapEditor :text="textToDisplay" />
    
    <!-- Show streaming indicator -->
    <div v-if="isStreaming && !textToDisplay" class="animate-pulse">
      Generating...
    </div>
  </div>
</template>
```

## Phase 1 Modified Files Summary

### NEW Code Added To:
- `components/write/utils/markdownRevisionParser.ts` - Add `parsePartialMarkdownRevisions()`
- `entrypoints/background.ts` - Add streaming message handler
- `entrypoints/background/revisionHandler.ts` - Add `handleGenerateRevisionStream()`  
- `components/write/api/index.ts` - Add `generateRevisionStream()`
- `useWritingRevisionHandler.ts` - Add streaming state + message listener
- `components/write/components/RevisedTextViewer.vue` - Add streaming props
- `components/write/components/WritingOutputSection.vue` - Pass streaming state

### Existing Code Modified:
- `useWritingRevisionHandler.ts` - Update `initiateGrammarCheck()` to use streaming
- `components/write/components/WritingOutputSection.vue` - Pass new props to RevisedTextViewer

**Total Phase 1 Changes: ~200-300 lines of code across 7 files**

## Critical Differences from Client Implementation

### ✅ **Patterns to Follow from Client**
- **Streaming State Management**: `isStreamingRevision`, `isLoadingVocabularyCards`
- **Progressive Parsing**: `parsePartialMarkdownRevisions()` with error handling (port function)
- **UI Progressive Display**: Field-level undefined vs progressive content
- **Refresh Variation**: Different prompts while maintaining tone consistency
- **History Management**: Append revisions for refresh, maintain navigation
- **Placeholder Revision Logic**: Add empty revision immediately on refresh, switch to it

### ❌ **Key Architecture Differences**

#### Client (Direct Streaming)
```typescript
const generator = await revisionApi.generateRevisionStream(prompt);
for await (const chunk of generator) {
  // Process chunks directly
}
```

#### Extension (Message-Based Simulation)
```typescript
const streamId = await sendMessage('startRevisionStream', { prompt });
onMessage('streamChunk', (message) => {
  if (message.streamId === streamId) {
    // Process chunks from messages
  }
});
```

## Client Implementation Reference

The plan is based on the successful streaming implementation in the client as documented in:
- `/Users/<USER>/code/pickvocab/pickvocab-client/docs/write/writing-assistant-streaming-implementation-plan.md`
- `/Users/<USER>/code/pickvocab/pickvocab-client/docs/write/refresh-prompt-variation-plan.md`

**Key Client Files to Port Logic From**:
- `useWriteRevisionHandler.ts` - `callLLMAndParseResponseStream()` pattern
- `useRevisionApi.ts` - `generateRevisionStream()` implementation
- `refreshRevisionPrompt.ts` and `refreshVocabularyPrompt.ts` - Exact prompt functions
- `markdownRevisionParser.ts` - `parsePartialMarkdownRevisions()` function
- UI components streaming patterns

## Architecture Considerations

### Web Extension Specific Constraints
- **Message Passing Limitation**: Cannot pass AsyncGenerators through messages
- **Tab Context**: Need proper tab ID tracking for streaming messages
- **Content Script Lifecycle**: Handle streaming cleanup on page navigation
- **Memory Management**: Proper cleanup for message listeners and streaming state

### Streaming Simulation Strategy
1. **Background Script**: Process true streaming internally
2. **Progressive Messaging**: Send accumulated chunks via `sendMessage()`
3. **Content Script**: Accumulate chunks and simulate streaming display
4. **Error Handling**: Timeout mechanisms for abandoned streams
5. **Cleanup**: Proper listener removal and state reset

### Compatibility Maintained
- **Existing Features**: Vocabulary integration, grammar check, history all preserved
- **Backward Compatibility**: Fallback to non-streaming when needed
- **Provider Agnostic**: Works with all configured LLM providers
- **Performance**: Efficient message-based chunk processing and UI updates
- **Extension Patterns**: Maintains existing popup positioning, content script integration