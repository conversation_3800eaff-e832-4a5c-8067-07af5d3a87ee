import type { ChatMessage, ChatSource, ChatResponse } from "pickvocab-dictionary";

const CLIENT_API_URL = import.meta.env.WXT_CLIENT_API_URL;

export class NvidiaNimChatClient implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName: string;
  maxTokens = 8192;
  isThinkingModel = false;
  messages: unknown[] = [];

  constructor(config: { modelName: string, modelId: number, maxToken?: number, apiKey: string, isThinkingModel?: boolean }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config?.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.isThinkingModel = config.isThinkingModel ?? false;
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages;
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    const response = await fetch(`${CLIENT_API_URL}/api/nvidiaNimChat/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        isThinkingModel: this.isThinkingModel,
        endpoint: 'sendMessage',
        args: [this.messages, message]
      })
    });
    
    const result = await response.json();
    this.messages.push({ role: 'user', content: message });
    this.messages.push({
      role: 'assistant',
      content: result.message
    });
    return result as ChatResponse;
  }
}