import type {
  BaseWordEntry,
  BaseWordInContextEntry,
  DefinitionForLanguagePromptResult,
  DictionarySource,
  ExamplePromptResult,
  SynonymPromptResult,
  StreamingWordInContextEntry
} from "pickvocab-dictionary";

const CLIENT_API_URL = import.meta.env.WXT_CLIENT_API_URL;

export class NvidiaNimSourceClient implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 8192;
  isThinkingModel = false;

  constructor(config: { modelId: number, modelName: string, maxToken?: number, apiKey?: string, isThinkingModel?: boolean }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.isThinkingModel = config.isThinkingModel ?? false;
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const response = await fetch(`${CLIENT_API_URL}/api/nvidiaNim/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        endpoint: 'listAllMeanings',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        isThinkingModel: this.isThinkingModel,
        args: [word]
      })
    });
    return response.json() as Promise<BaseWordEntry>;
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const response = await fetch(`${CLIENT_API_URL}/api/nvidiaNim/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        endpoint: 'listAllMeaningsForLanguage',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        isThinkingModel: this.isThinkingModel,
        args: [input, language]
      })
    });
    return response.json() as Promise<DefinitionForLanguagePromptResult>;
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const response = await fetch(`${CLIENT_API_URL}/api/nvidiaNim/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        endpoint: 'getMoreExamples',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        isThinkingModel: this.isThinkingModel,
        args: [input]
      })
    });
    return response.json() as Promise<ExamplePromptResult>;
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const response = await fetch(`${CLIENT_API_URL}/api/nvidiaNim/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        endpoint: 'getMoreSynonymsForDefinition',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        isThinkingModel: this.isThinkingModel,
        args: [input]
      })
    });
    return response.json() as Promise<SynonymPromptResult>;
  }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    const response = await fetch(`${CLIENT_API_URL}/api/nvidiaNim/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        endpoint: 'getMeaningInContext',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        isThinkingModel: this.isThinkingModel,
        args: [word, context, offset]
      })
    });
    return response.json() as Promise<BaseWordInContextEntry>;
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    const response = await fetch(`${CLIENT_API_URL}/api/nvidiaNim/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        endpoint: 'getMeaningInContextShort',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        isThinkingModel: this.isThinkingModel,
        args: [word, context, offset]
      })
    });
    return response.json() as Promise<BaseWordInContextEntry>;
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string
  ): Promise<string> {
    const response = await fetch(`${CLIENT_API_URL}/api/nvidiaNim/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        endpoint: 'getMeaningInContextShortForLanguage',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        isThinkingModel: this.isThinkingModel,
        args: [word, context, offset, language]
      })
    });
    return response.json() as Promise<string>;
  }

  private async *createStreamingGenerator(endpoint: string, args: any[]): AsyncGenerator<{ chunk: string; isComplete: boolean; result?: any }> {
    const response = await fetch(`${CLIENT_API_URL}/api/nvidiaNimStream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        endpoint,
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        isThinkingModel: this.isThinkingModel,
        args
      })
    });

    if (!response.body) {
      throw new Error('No response body received from streaming endpoint');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              yield data;
              
              if (data.isComplete) {
                return;
              }
            } catch (e) {
              // Skip malformed JSON lines
              continue;
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  async *getMeaningInContextShortStream(word: string, context: string, offset: number): AsyncGenerator<{ chunk: string; isComplete: boolean; result?: BaseWordInContextEntry }> {
    yield* this.createStreamingGenerator('getMeaningInContextShortStream', [word, context, offset]);
  }

  async *getMeaningInContextStream(word: string, context: string, offset: number): AsyncGenerator<{ chunk: string; isComplete: boolean; result?: StreamingWordInContextEntry }> {
    yield* this.createStreamingGenerator('getMeaningInContextStream', [word, context, offset]);
  }

  async *getMeaningInContextShortForLanguageStream(
    word: string,
    context: string,
    offset: number,
    language: string
  ): AsyncGenerator<{ chunk: string; isComplete: boolean; result?: string }> {
    yield* this.createStreamingGenerator('getMeaningInContextShortForLanguageStream', [word, context, offset, language]);
  }
}