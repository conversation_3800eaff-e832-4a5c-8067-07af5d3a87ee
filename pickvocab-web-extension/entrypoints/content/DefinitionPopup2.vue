<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useContentStore } from './store';
import ExplanationView from './ExplanationView.vue';
import Spinner from '@/components/Spinner.vue';
import DictionaryWordViewInfoAlert from '@/components/lookup/DictionaryWordViewInfoAlert.vue';
import DictionaryWordViewErrorAlert from '@/components/lookup/DictionaryWordViewErrorAlert.vue';
import { SubmitButton } from '@/components/ui/submit-button';
import { useAuthStore } from '../popup/stores/auth';
import { sendMessage } from 'webext-bridge/content-script';
import { storage } from 'wxt/storage';
import { useLLMStore } from '../popup/stores/llm';
import { useDictionaryLookup2 } from './lookup2/useDictionaryLookup2';
import type { WordInContextEntry } from 'pickvocab-dictionary';
import { BaseContextCard } from '@/utils/card';

// Content store: holds selected word/context/offset
const contentStore = useContentStore();
const { word, context, offset } = storeToRefs(contentStore);

// Concurrent-stream lookup composable
const {
  wordEntry,
  isLoading,
  isStreamingSimple,
  isStreamingDetailed,
  errorMessage,
  activeSimpleLanguages,
  lookupSimpleEnglish,
  lookupSimpleLanguage,
  lookupDetailed,
} = useDictionaryLookup2();

// LLM store for API key alert and model lookup
const llmStore = useLLMStore();

// Local state
const isDetailed = ref(false);
const selectedSimpleViewLanguage = ref<string>('English');
const currentView = ref<'definition' | 'addCard'>('definition');
const addCardError = ref('');
const cardUrl = ref('');
const isSaved = ref(false);
const isSignInLoading = ref(false);
const authStore = useAuthStore();

// Derived state
const isStreamingSimpleCurrentLang = computed(() => {
  if (!isStreamingSimple.value) return false;
  const lang = selectedSimpleViewLanguage.value || 'English';
  return activeSimpleLanguages.value.has(lang);
});
const llmModel = computed(() => {
  const id = wordEntry.value?.llm_model;
  return typeof id === 'number' ? llmStore.getModelById(id) : undefined;
});
const showInfoMessage = computed(() => llmStore.shouldShowAPIKeyAlert());

function setupApiKey() {
  sendMessage("openOptionsPage", {}, "background");
}

async function hideApiKeyAlert() {
  llmStore.lastShowAPIKeyAlert = Date.now();
}

// Helper: ensure simple definition exists
function ensureSimpleDefinition(lang: string) {
  // If we already have a central entry, just fire the language lookup without
  // passing new word/context/offset – that keeps the reduced-context triple
  // stable and prevents the state layer from thinking it’s a different word.

  if (wordEntry.value) {
    if (lang === 'English') {
      if (!wordEntry.value.definitionShort?.explanation) {
        lookupSimpleEnglish(wordEntry.value.word, wordEntry.value.context, wordEntry.value.offset);
      }
    } else {
      const has = wordEntry.value.definitionShort?.languages?.[lang]?.explanation;
      if (!has) {
        lookupSimpleLanguage(lang); // no extra params → reuse existing entry
      }
    }
    return;
  }

  // No entry yet (e.g. first non-English lookup): pass full parameters so the
  // stream can create the base entry.
  if (!word.value || !context.value || offset.value === undefined) return;
  if (lang === 'English') {
    lookupSimpleEnglish(word.value, context.value, offset.value);
  } else {
    lookupSimpleLanguage(lang, { word: word.value, context: context.value, offset: offset.value });
  }
}

// Initial lookup on mount
async function handleLookup() {
  if (!word.value || !context.value || offset.value === undefined) return;
  if (isDetailed.value) {
    lookupDetailed({ word: word.value, context: context.value, offset: offset.value });
  } else {
    if (selectedSimpleViewLanguage.value === 'English') {
      lookupSimpleEnglish(word.value, context.value, offset.value);
    } else {
      lookupSimpleLanguage(selectedSimpleViewLanguage.value, { word: word.value, context: context.value, offset: offset.value });
    }
  }
}

function refresh(language?: string) {
  if (!wordEntry.value) return;
  if (isDetailed.value) {
    lookupDetailed();
  } else {
    const lang = language ?? selectedSimpleViewLanguage.value ?? 'English';
    if (lang === 'English') {
      lookupSimpleEnglish(wordEntry.value.word, wordEntry.value.context, wordEntry.value.offset);
    } else {
      lookupSimpleLanguage(lang);
    }
  }
}

// Card creation logic
async function addCard(entry: WordInContextEntry, callback?: () => void) {
  if (!authStore.isAuthenticated) {
    addCardError.value = 'You need to sign in to save this word';
    currentView.value = 'addCard';
    callback?.();
    return;
  }
  try {
    const baseCard: BaseContextCard = { wordInContext: entry };
    const card = (await sendMessage('contextCard:create', { card: baseCard as any }, 'background')) as any as WordInContextEntry;
    cardUrl.value = `${import.meta.env.WXT_CLIENT_API_URL}/app/cards/${card.id}`;
    currentView.value = 'addCard';
    isSaved.value = true;
  } catch (err) {
    addCardError.value = `Failed to create card: ${err}`;
  } finally {
    callback?.();
  }
}

async function signIn() {
  await sendMessage('googleSignIn', {}, 'background');
  authStore.isHydrated = false;
  await authStore.hydrate();
}

async function addCardSignIn() {
  isSignInLoading.value = true;
  try {
    await signIn();
    await addCard(wordEntry.value!, () => {
      isSignInLoading.value = false;
    });
  } catch {
    isSignInLoading.value = false;
  }
}

function backToDefinitionView(status = false) {
  currentView.value = 'definition';
  addCardError.value = '';
  cardUrl.value = '';
  isSaved.value = false;
}


// Lifecycle & watchers
onMounted(async () => {
  // Load user's preferred view mode
  const savedIsDetailed = await storage.getItem('local:isDetailedView');
  if (savedIsDetailed !== null && typeof savedIsDetailed === 'boolean') {
    isDetailed.value = savedIsDetailed;
  }

  // Load user's preferred language for simple view
  const savedLang = await storage.getItem('local:selectedSimpleLanguage');
  if (typeof savedLang === 'string' && savedLang) {
    selectedSimpleViewLanguage.value = savedLang;
  }

  await Promise.allSettled([llmStore.hydrate(), authStore.hydrate()]);
  await handleLookup();
});


watch(isDetailed, (newVal) => {
  storage.setItem('local:isDetailedView', isDetailed.value);
  if (!wordEntry.value) return;
  if (newVal) {
    if (!wordEntry.value.definition) lookupDetailed();
  } else {
    ensureSimpleDefinition(selectedSimpleViewLanguage.value);
  }
});

watch(selectedSimpleViewLanguage, lang => {
  if (lang) ensureSimpleDefinition(lang);
  // Persist user's preferred language
  storage.setItem('local:selectedSimpleLanguage', lang);
});

</script>

<template>
  <div id="pickvocab-popup-container"
    class="!visible bg-white w-[600px] max-h-[400px] overflow-auto p-6 border border-gray-200 shadow-2xl rounded">
    <div v-if="currentView === 'definition'">
      <DictionaryWordViewInfoAlert v-if="wordEntry && showInfoMessage" class="mb-4" @setup="setupApiKey()"
        @dismiss="hideApiKeyAlert()">
      </DictionaryWordViewInfoAlert>
      <DictionaryWordViewErrorAlert class="mb-4" v-if="errorMessage" @retry="refresh()" @setup="setupApiKey()"
        :message="errorMessage" :is-active-user-model="llmStore.activeUserModel ? true : false" />

      <ExplanationView
        v-if="wordEntry"
        :word="word"
        :word-entry="wordEntry"
        :llm-model="llmModel"
        :is-loading="isLoading"
        :is-streaming="isDetailed ? isStreamingDetailed : isStreamingSimpleCurrentLang"
        v-model:is-detailed="isDetailed"
        v-model:selected-simple-view-language="selectedSimpleViewLanguage"
        @add-card="addCard"
        @refresh="(lang) => refresh(lang)"
        @simple-lookup-for-language="(lang) => ensureSimpleDefinition(lang)"
      />
      <div v-else-if="!errorMessage" class="w-full h-full flex items-center justify-center">
        <Spinner 
          :size="6" 
        />
      </div>
    </div>
    <div v-else-if="currentView === 'addCard'">
      <div v-if="isSaved" class="flex flex-col items-center justify-center">
        <p class="text-lg font-semibold text-gray-800">
          Word saved successfully!
        </p>
        <a :href="cardUrl" target="_blank" class="text-blue-700 hover:text-blue-800 mt-2">
          View card
        </a>
        <div class="flex mt-4 space-x-2">
          <button type="button" @click="backToDefinitionView(true)"
            class="text-blue-800 bg-transparent border border-blue-800 hover:bg-blue-900 hover:text-white focus:ring-4 focus:outline-none focus:ring-blue-200 font-medium rounded-lg text-xs px-3 py-1.5 text-center"
            data-dismiss-target="#alert-additional-content-1" aria-label="Close">
            Back
          </button>
        </div>
      </div>
      <div v-else>
        <p class="text-lg font-semibold text-gray-800">{{ addCardError }}</p>
        <div class="flex mt-4 space-x-2">
          <SubmitButton
            class="text-white bg-blue-800 hover:bg-blue-900 focus:ring-4 focus:outline-none focus:ring-blue-200 font-medium rounded-lg text-xs px-3 py-1.5 text-center inline-flex items-center"
            text="Sign In"
            loading-text="Signing in..."
            :is-loading="isSignInLoading" 
            @click="addCardSignIn()" 
            is-primary
          />
          <button type="button" @click="backToDefinitionView(false)"
            class="text-blue-800 bg-transparent border border-blue-800 hover:bg-blue-900 hover:text-white focus:ring-4 focus:outline-none focus:ring-blue-200 font-medium rounded-lg text-xs px-3 py-1.5 text-center"
            data-dismiss-target="#alert-additional-content-1" aria-label="Close">
            Back
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* reuse existing fade styles if needed */
</style> 