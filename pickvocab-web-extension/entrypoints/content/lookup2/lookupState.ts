import { ref, onScopeDispose } from 'vue';
import { useEventBus } from '@vueuse/core';
import type { WordInContextEntry } from 'pickvocab-dictionary';
import { useLLMStore } from '../../popup/stores/llm';
import type { LookupEvent } from './events';
import type { BaseWordInContextDefinition } from 'pickvocab-dictionary';
import { currentSimpleRequestId, currentLanguageRequestId, currentDetailedRequestId } from './requestIds';

/**
 * Reactive state layer for the extension popup. Listens to the global
 * lookup-event bus and mutates a central `wordEntry` ref so the UI can
 * reactively consume updates coming from background streams.
 */
export function useLookupState() {
  const wordEntry = ref<WordInContextEntry>();
  const isLoading = ref(false);
  const isStreamingSimple = ref(false);
  const isStreamingDetailed = ref(false);
  const errorMessage = ref<string | null>(null);

  // Track which languages currently have an active simple stream.
  const activeSimpleLanguages = ref<Set<string>>(new Set());

  const llmStore = useLLMStore();
  const bus = useEventBus<LookupEvent>('lookup-events');

  const stopListener = bus.on((event) => {
    switch (event.type) {
      case 'StreamStarted': {
        if (event.streamType === 'simple') {
          isLoading.value = false;
          isStreamingSimple.value = true;
          errorMessage.value = null;

          const lang = event.language ?? 'English';
          if (!activeSimpleLanguages.value.has(lang)) {
            activeSimpleLanguages.value = new Set([...activeSimpleLanguages.value, lang]);
          }

          const needsReset =
            !wordEntry.value ||
            wordEntry.value.word !== event.requestDetails.word ||
            wordEntry.value.context !== event.requestDetails.context ||
            wordEntry.value.offset !== event.requestDetails.offset;

          if (needsReset) {
            wordEntry.value = {
              id: `temp-${Date.now()}`,
              word: event.requestDetails.word,
              context: event.requestDetails.context,
              offset: event.requestDetails.offset,
              definitionShort: { explanation: '' },
              definition: undefined,
              llm_model: llmStore.activeUserModel?.id || llmStore.pickvocabModel?.id || 1,
            } as WordInContextEntry;
          }

          if (lang === 'English') {
            if (!wordEntry.value!.definitionShort) {
              wordEntry.value!.definitionShort = { explanation: '' };
            }
            wordEntry.value!.definitionShort.explanation = '';
          } else {
            if (!wordEntry.value!.definitionShort) {
              wordEntry.value!.definitionShort = { explanation: '' };
            }
            if (!wordEntry.value!.definitionShort.languages) {
              wordEntry.value!.definitionShort.languages = {};
            }
            wordEntry.value!.definitionShort.languages![lang] = { explanation: '' };
          }
        }

        if (event.streamType === 'detailed') {
          isLoading.value = false;
          isStreamingDetailed.value = true;
          errorMessage.value = null;

          const needsReset =
            !wordEntry.value ||
            wordEntry.value.word !== event.requestDetails.word ||
            wordEntry.value.context !== event.requestDetails.context ||
            wordEntry.value.offset !== event.requestDetails.offset;

          if (needsReset) {
            wordEntry.value = {
              id: `temp-${Date.now()}`,
              word: event.requestDetails.word,
              context: event.requestDetails.context,
              offset: event.requestDetails.offset,
              definitionShort: { explanation: '' },
              definition: undefined,
              llm_model: llmStore.activeUserModel?.id || llmStore.pickvocabModel?.id || 1,
            } as WordInContextEntry;
          }

          wordEntry.value!.definition = undefined;
        }
        break;
      }

      case 'SimpleTextChunk': {
        const lang = event.language ?? 'English';
        if (
          (lang === 'English' && event.requestId === currentSimpleRequestId()) ||
          (lang !== 'English' && event.requestId === currentLanguageRequestId(lang))
        ) {
          if (!wordEntry.value?.definitionShort) return;

          if (lang === 'English') {
            wordEntry.value.definitionShort.explanation += event.chunk;
          } else {
            if (!wordEntry.value.definitionShort.languages) {
              wordEntry.value.definitionShort.languages = {};
            }
            const langObj = wordEntry.value.definitionShort.languages[lang] || { explanation: '' };
            langObj.explanation += event.chunk;
            wordEntry.value.definitionShort.languages[lang] = langObj;
          }
        }
        break;
      }

      case 'DetailedPartialUpdate': {
        if (event.requestId !== currentDetailedRequestId()) break;
        if (!wordEntry.value) break;
        const defPatch: Partial<BaseWordInContextDefinition> = event.definition;
        wordEntry.value.definition = { ...wordEntry.value.definition, ...defPatch } as BaseWordInContextDefinition;
        break;
      }

      case 'StreamCompleted': {
        if (event.streamType === 'simple') {
          const lang = event.language ?? 'English';
          if (activeSimpleLanguages.value.has(lang)) {
            const next = new Set(activeSimpleLanguages.value);
            next.delete(lang);
            activeSimpleLanguages.value = next;
          }
          if (activeSimpleLanguages.value.size === 0) {
            isStreamingSimple.value = false;
          }
        } else if (event.streamType === 'detailed') {
          isStreamingDetailed.value = false;
        }
        break;
      }

      case 'StreamError': {
        if (event.streamType === 'simple') {
          const lang = event.language ?? 'English';
          if (activeSimpleLanguages.value.has(lang)) {
            const next = new Set(activeSimpleLanguages.value);
            next.delete(lang);
            activeSimpleLanguages.value = next;
          }
          if (activeSimpleLanguages.value.size === 0) {
            isStreamingSimple.value = false;
          }
        } else if (event.streamType === 'detailed') {
          isStreamingDetailed.value = false;
        }
        errorMessage.value = event.error;
        break;
      }
    }
  });

  // Clean up the event listener when the component is unmounted
  onScopeDispose(() => {
    stopListener();
  });

  return {
    wordEntry,
    isLoading,
    isStreamingSimple,
    isStreamingDetailed,
    errorMessage,
    activeSimpleLanguages,
  };
} 