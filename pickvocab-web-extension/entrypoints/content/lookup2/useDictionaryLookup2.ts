import { computed } from 'vue';
import { useEventBus } from '@vueuse/core';
import { Dictionary, type DictionarySource } from 'pickvocab-dictionary';
import { useLLMStore } from '../../popup/stores/llm';
import type { LookupEvent } from './events';
import { useLookupState } from './lookupState';
import { lookupSimpleEnglish } from './simpleEnglishStream';
import { lookupSimpleLanguage } from './simpleLanguageStream';
import { lookupDetailed } from './detailedStream';
import { useIdManager } from './idManager';

export function useDictionaryLookup2() {
  // Central reactive state
  const {
    wordEntry,
    isLoading,
    isStreamingSimple,
    isStreamingDetailed,
    errorMessage,
    activeSimpleLanguages,
  } = useLookupState();

  // Persist to server via background bridge
  useIdManager(wordEntry);

  const llmStore = useLLMStore();

  // Reactive dictionary instance depending on active LLM.
  const dictionary = computed(() => {
    let sources: DictionarySource[] = [llmStore.pickvocabDictionarySource];
    if (llmStore.activeUserModel) {
      sources = [llmStore.createDictionarySource(llmStore.activeUserModel), ...sources];
    }
    return new Dictionary(sources);
  });

  // Wrappers ---------------------------------------------------------------
  const lookupSimpleEnglishWrapper = async (word: string, context: string, offset: number) => {
    const bus = useEventBus<LookupEvent>('lookup-events');
    await lookupSimpleEnglish({ word, context, offset, dictionary: dictionary.value, bus, wordEntryRef: wordEntry });
  };

  const lookupSimpleLanguageWrapper = async (
    language: string,
    opts?: { word: string; context: string; offset: number },
  ) => {
    const bus = useEventBus<LookupEvent>('lookup-events');
    await lookupSimpleLanguage({
      language,
      dictionary: dictionary.value,
      bus,
      wordEntryRef: wordEntry,
      word: opts?.word,
      context: opts?.context,
      offset: opts?.offset,
    });
  };

  const lookupDetailedWrapper = async (opts?: { word: string; context: string; offset: number }) => {
    const bus = useEventBus<LookupEvent>('lookup-events');
    await lookupDetailed({
      dictionary: dictionary.value,
      bus,
      wordEntryRef: wordEntry,
      word: opts?.word,
      context: opts?.context,
      offset: opts?.offset,
    });
  };

  return {
    wordEntry,
    isLoading,
    isStreamingSimple,
    isStreamingDetailed,
    errorMessage,
    activeSimpleLanguages,
    lookupSimpleEnglish: lookupSimpleEnglishWrapper,
    lookupSimpleLanguage: lookupSimpleLanguageWrapper,
    lookupDetailed: lookupDetailedWrapper,
  };
} 