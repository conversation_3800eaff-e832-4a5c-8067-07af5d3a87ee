import type { Ref } from 'vue';
import { ref, onScopeDispose } from 'vue';
import { useEventBus } from '@vueuse/core';
import type { WordInContextEntry, BaseWordInContextEntry } from 'pickvocab-dictionary';
import type { LookupEvent } from './events';
import { sendMessage } from 'webext-bridge/content-script';

/**
 * Handles creation / update of `WordInContextEntry` via background-script
 * bridge messages. Mirrors the logic of the client-side `idManager` but
 * without URL manipulation and using `webext-bridge` instead of REST calls.
 */
export function useIdManager(wordEntry: Ref<WordInContextEntry | undefined>) {
  const idResolutionInProgress = ref(false);
  const pendingServerUpdate = ref(false);

  const bus = useEventBus<LookupEvent>('lookup-events');

  const stop = bus.on(async (event) => {
    if (event.type !== 'StreamCompleted') return;

    const entry = wordEntry.value;
    if (!entry) return;

    const hasRealId = typeof entry.id === 'number';

    // Real ID – PUT update via background
    if (hasRealId) {
      try {
        console.info('[IDManager] PUT update', entry.id);
        await sendMessage('wordInContext:put', { word: entry as any }, 'background');
      } catch (e) {
        console.error('[idManager] PUT failed', e);
      }
      return;
    }

    // Temporary ID handling
    if (idResolutionInProgress.value) {
      pendingServerUpdate.value = true;
      return;
    }

    idResolutionInProgress.value = true;

    try {
      const base: BaseWordInContextEntry = {
        word: entry.word,
        context: entry.context,
        offset: entry.offset,
        definitionShort: entry.definitionShort,
        definition: entry.definition,
        llm_model: entry.llm_model,
      };

      console.info('[IDManager] CREATE entry');
      const created = (await sendMessage('wordInContext:create', { base: base as any }, 'background')) as any as WordInContextEntry;
      wordEntry.value = { ...created } as WordInContextEntry;

      if (pendingServerUpdate.value) {
        pendingServerUpdate.value = false;
        console.info('[IDManager] pending PUT after create');
        await sendMessage('wordInContext:put', { word: wordEntry.value as any }, 'background');
      }
    } catch (e) {
      console.error('[idManager] create failed', e);
    } finally {
      idResolutionInProgress.value = false;
    }
  });

  // Remove listener when the component using this composable is unmounted to
  // avoid stacking multiple idManager instances (which leads to duplicate
  // create calls once a second popup is opened).
  onScopeDispose(() => {
    stop();
  });
} 