import { Dictionary } from 'pickvocab-dictionary';
import { useEventBus } from '@vueuse/core';
import type { Ref } from 'vue';
import type { WordInContextEntry } from 'pickvocab-dictionary';
import type { LookupEvent } from './events';
import { reduceContext } from '../reduceContext';
import { nextDetailedRequestId, currentDetailedRequestId } from './requestIds';
import { useLLMStore } from '../../popup/stores/llm';

interface DetailedLookupParams {
  dictionary: Dictionary;
  bus: ReturnType<typeof useEventBus<LookupEvent>>;
  wordEntryRef: Ref<WordInContextEntry | undefined>;
  word?: string;
  context?: string;
  offset?: number;
}

function ensureBaseEntry(
  wordEntryRef: Ref<WordInContextEntry | undefined>,
  props: { word?: string; context?: string; offset?: number },
): boolean {
  const { word, context, offset } = props;

  const needsNew =
    !wordEntryRef.value ||
    (word && wordEntryRef.value.word !== word) ||
    (context && wordEntryRef.value.context !== context) ||
    (offset !== undefined && wordEntryRef.value.offset !== offset);

  if (!needsNew) return true;
  if (!word || !context || offset === undefined) return false;

  const llmStore = useLLMStore();
  wordEntryRef.value = {
    id: `temp-${Date.now()}`,
    word,
    context,
    offset,
    definitionShort: { explanation: '' },
    definition: undefined,
    llm_model: llmStore.activeUserModel?.id || llmStore.pickvocabModel?.id || 1,
  } as WordInContextEntry;

  return true;
}

async function performDetailedStream(
  entry: WordInContextEntry,
  dictionary: Dictionary,
  bus: ReturnType<typeof useEventBus<LookupEvent>>,
  currentRequestId: number,
  wordEntryRef: Ref<WordInContextEntry | undefined>,
): Promise<boolean> {
  try {
    const generator = dictionary.getMeaningInContextStream(entry.word, entry.context, entry.offset);

    for await (const { chunk, isComplete, result } of generator) {
      if (currentDetailedRequestId() !== currentRequestId) {
        return true;
      }

      if (result?.definition) {
        bus.emit({
          type: 'DetailedPartialUpdate',
          definition: result.definition ?? {},
          requestId: currentRequestId,
        });
      }

      if (isComplete) {
        bus.emit({
          type: 'StreamCompleted',
          streamType: 'detailed',
          requestId: currentRequestId,
        });
      }
    }
    return true;
  } catch (err) {
    return false;
  }
}

async function performDetailedNonStreaming(
  entry: WordInContextEntry,
  dictionary: Dictionary,
  bus: ReturnType<typeof useEventBus<LookupEvent>>,
  currentRequestId: number,
  wordEntryRef: Ref<WordInContextEntry | undefined>,
) {
  try {
    const updatedEntry = await dictionary.getMeaningInContext(entry.word, entry.context, entry.offset);
    if (wordEntryRef.value) {
      const originalId = wordEntryRef.value.id;
      wordEntryRef.value = { ...wordEntryRef.value, ...updatedEntry, id: originalId };
    }

    bus.emit({
      type: 'StreamCompleted',
      streamType: 'detailed',
      requestId: currentRequestId,
    });
  } catch (nonStreamError: any) {
    bus.emit({
      type: 'StreamError',
      streamType: 'detailed',
      error: nonStreamError.message,
      requestId: currentRequestId,
    });
  }
}

export async function lookupDetailed({
  dictionary,
  bus,
  wordEntryRef,
  word,
  context,
  offset,
}: DetailedLookupParams): Promise<void> {
  const prepared = ensureBaseEntry(wordEntryRef, { word, context, offset });
  if (!prepared) return;

  const entry = wordEntryRef.value!;
  const currentRequestId = nextDetailedRequestId();

  const reduced = reduceContext(entry.context, entry.word, entry.offset, 3);

  bus.emit({
    type: 'StreamStarted',
    streamType: 'detailed',
    requestDetails: {
      word: reduced.selectedText,
      context: reduced.text,
      offset: reduced.offset,
    },
  });

  const streamed = await performDetailedStream(
    { ...entry, context: reduced.text, offset: reduced.offset, word: reduced.selectedText },
    dictionary,
    bus,
    currentRequestId,
    wordEntryRef,
  );

  if (!streamed) {
    await performDetailedNonStreaming(entry, dictionary, bus, currentRequestId, wordEntryRef);
  }
} 